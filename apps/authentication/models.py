"""
Authentication models for CLEAR platform.
Core security models: User, Role, UserRole, Organization, and UserProfile.
"""

from __future__ import annotations

import logging
import uuid
from typing import Any, ClassVar, Dict, List, Optional, Union

from django.contrib.auth.models import <PERSON>bstract<PERSON><PERSON>, BaseUserManager
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.core.validators import URLVali<PERSON>tor
from django.db import models
from django.utils.translation import gettext_lazy as _

from apps.core.types import <PERSON><PERSON>ND<PERSON>, OrganizationId, PrimaryKey, RoleSlug, UserId

logger = logging.getLogger(__name__)


class UserManager(BaseUserManager["User"]):
    """Custom user manager for email-based authentication."""

    def create_user(self, email: str, password: Optional[str] = None, **extra_fields: Any) -> User:
        """Create and return a regular user with an email and password."""
        if not email:
            raise ValueError(_("Email address is required"))
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        if password:
            user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email: str, password: Optional[str] = None, **extra_fields: Any) -> User:
        """Create and return a superuser with an email and password."""
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        return self.create_user(email, password, **extra_fields)


class User(AbstractUser):
    """Custom user model using email as the primary identifier."""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    username = None  # Disable username field
    email = models.EmailField(_("email address"), unique=True)
    phone_number = models.CharField(_("phone number"), max_length=20, blank=True)
    timezone = models.CharField(
        _("timezone"),
        max_length=50,
        default="UTC",
        help_text=_("User's preferred timezone (e.g., America/New_York)"),
    )
    # Organization relationship - note: we use UserRole for multi-org support
    # This field represents the user's primary organization for convenience
    primary_organization = models.ForeignKey(
        "Organization",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="primary_users",
        help_text=_("User's primary organization (derived from primary UserRole)"),
    )
    is_verified = models.BooleanField(_("verified"), default=False)
    mfa_enabled = models.BooleanField(_("MFA enabled"), default=False)
    last_password_change = models.DateTimeField(_("last password change"), null=True)
    security_questions = models.JSONField(_("security questions"), default=dict, blank=True)
    failed_login_attempts = models.PositiveIntegerField(_("failed login attempts"), default=0)
    temporary_lockout_until = models.DateTimeField(_("temporary lockout until"), null=True)

    objects = UserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    class Meta:
        verbose_name = _("user")
        verbose_name_plural = _("users")
        ordering = ["email"]
        indexes = [
            models.Index(fields=["email"]),
            models.Index(fields=["is_verified", "is_active"]),
            models.Index(fields=["last_login"]),
        ]

    def __str__(self):
        return self.email

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def get_short_name(self):
        return self.first_name

    @property
    def organization(self):
        """Get user's primary organization."""
        # First check if primary_organization is set
        if self.primary_organization:
            return self.primary_organization
        # Fall back to getting from primary UserRole
        primary_role = self.user_roles.filter(is_primary=True).first()
        return primary_role.organization if primary_role else None

    @property
    def organization_id(self):
        """Get user's primary organization ID for role checking."""
        return self.primary_organization_id if self.primary_organization_id else None

    def get_role_level(self, organization_id):
        """Get user's role level in a specific organization."""
        try:
            user_role = self.user_roles.get(organization_id=organization_id, is_primary=True)
            return user_role.role.level
        except (UserRole.DoesNotExist, AttributeError, ValueError) as e:
            # User might not have a role in this organization
            logger.debug(f"No role found for user {self.id} in org {organization_id}: {e}")
            return None

    def get_organizations(self):
        """Get all organizations the user has access to."""
        return [ur.organization for ur in self.user_roles.select_related("organization")]

    def has_organization_access(self, organization):
        """Check if user has access to a specific organization."""
        return self.user_roles.filter(organization=organization).exists()

    def get_organization_role(self, organization):
        """Get user's role in a specific organization."""
        user_role = self.user_roles.filter(organization=organization).first()
        return user_role.role if user_role else None

    def password_change_required(self, days=90):
        """Check if password change is required based on age."""
        if not self.last_password_change:
            return True
        from datetime import datetime, timezone

        password_age = datetime.now(timezone.utc) - self.last_password_change
        return password_age.days >= days

    def has_only_view_only_roles(self, organization=None):
        """Check if user has only view-only roles.

        Args:
            organization: Specific organization to check (optional)

        Returns:
            bool: True if all user's roles are view-only
        """
        user_roles = self.user_roles.select_related("role")

        if organization:
            user_roles = user_roles.filter(organization=organization)

        # If no roles, consider as view-only
        if not user_roles.exists():
            return True

        # Check if all roles are view-only
        return user_roles.filter(role__is_view_only=False).count() == 0

    def is_billable_user(self, organization=None):
        """Check if this user should be counted as billable.

        Args:
            organization: Specific organization to check (optional)

        Returns:
            bool: True if user is billable
        """
        # Platform operators are never billable
        if hasattr(self, "email") and self.email:
            # Check against common platform operator domains
            platform_domains = ["@egis-group.com"]
            for domain in platform_domains:
                if self.email.lower().endswith(domain):
                    return False

        # Users with only view-only roles are not billable
        return not self.has_only_view_only_roles(organization)


class Organization(models.Model):
    """Organization model for multi-tenant data isolation."""

    # Subscription tier choices
    SUBSCRIPTION_FREE = "free"
    SUBSCRIPTION_PRO = "pro"
    SUBSCRIPTION_ENTERPRISE = "enterprise"

    SUBSCRIPTION_CHOICES = [
        (SUBSCRIPTION_FREE, _("Free")),
        (SUBSCRIPTION_PRO, _("Professional")),
        (SUBSCRIPTION_ENTERPRISE, _("Enterprise")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(_("name"), max_length=255)
    slug = models.SlugField(_("slug"), unique=True)
    domain = models.CharField(_("domain"), max_length=255, blank=True)
    is_active = models.BooleanField(_("active"), default=True)

    # Subscription and settings
    subscription_tier = models.CharField(
        _("subscription tier"),
        max_length=20,
        choices=SUBSCRIPTION_CHOICES,
        default=SUBSCRIPTION_FREE,
    )
    settings = models.JSONField(
        _("settings"),
        default=dict,
        help_text=_("Organization-specific configuration settings"),
    )

    # Platform Operator Support
    support_access_enabled = models.BooleanField(
        _("support access enabled"),
        default=True,
        help_text=_("Allow platform operators (@egis-group.com) to access this organization for support"),
    )
    platform_operator_domain = models.CharField(
        _("platform operator domain"),
        max_length=100,
        default="egis-group.com",
        help_text=_("Email domain of the platform operator company"),
    )

    # Contact Information
    email = models.EmailField(_("email"), blank=True)
    phone = models.CharField(_("phone"), max_length=20, blank=True)
    address = models.TextField(_("address"), blank=True)
    city = models.CharField(_("city"), max_length=100, blank=True)
    state = models.CharField(_("state"), max_length=100, blank=True)
    zip_code = models.CharField(_("zip code"), max_length=10, blank=True)
    country = models.CharField(_("country"), max_length=100, blank=True)

    # Timezone and Localization
    timezone = models.CharField(
        _("timezone"),
        max_length=50,
        default="UTC",
        help_text=_("Timezone for the organization (e.g., America/New_York)"),
    )

    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        verbose_name = _("organization")
        verbose_name_plural = _("organizations")
        ordering = ["name"]
        indexes = [
            models.Index(fields=["slug"]),
            models.Index(fields=["domain"]),
            models.Index(fields=["is_active"]),
        ]

    def __str__(self):
        return self.name

    @property
    def is_platform_operator(self):
        """Check if this organization represents the platform operator."""
        return self.domain.lower() == self.platform_operator_domain.lower()

    def allows_platform_support(self):
        """Check if platform operators can access this organization for support."""
        return self.support_access_enabled and not self.is_platform_operator

    def get_users_with_role_or_higher(self, role_level, exclude_view_only=False):
        """Get users with a specific role level or higher (lower number = higher authority).

        Args:
            role_level: The role level threshold (e.g., 20 for Department Manager)
            exclude_view_only: If True, exclude users with view-only roles

        Returns:
            QuerySet of User objects
        """
        from django.contrib.auth import get_user_model
        from django.db.models import Q

        User = get_user_model()

        # Build the query for UserRole
        user_role_query = Q(organization=self, role__level__lte=role_level)  # Lower number = higher authority

        # Optionally exclude view-only roles
        if exclude_view_only:
            user_role_query &= Q(role__is_view_only=False)

        # Get users through UserRole relationship
        user_roles = UserRole.objects.filter(user_role_query).select_related("user", "role")

        # Get unique user IDs
        user_ids = user_roles.values_list("user_id", flat=True).distinct()

        # Return User queryset
        return User.objects.filter(id__in=user_ids)

    def get_billable_users_queryset(self, exclude_view_only=True):
        """Get queryset of users that count toward subscription limits.

        Args:
            exclude_view_only: If True (default), exclude users with view-only roles

        Excludes:
        - Platform operator users (@egis-group.com)
        - View-only role users (if exclude_view_only is True)

        Returns:
            QuerySet of User objects
        """
        from django.contrib.auth import get_user_model
        from django.db.models import Q

        User = get_user_model()

        # Get users through UserRole relationship
        user_roles_query = self.user_roles.select_related("user", "role")

        # Optionally exclude view-only roles
        if exclude_view_only:
            user_roles_query = user_roles_query.filter(Q(role__is_view_only=False))

        billable_users = User.objects.filter(id__in=user_roles_query.values_list("user_id", flat=True))

        # Exclude platform operator users
        billable_users = billable_users.exclude(email__iendswith=f"@{self.platform_operator_domain}")

        return billable_users

    def get_billable_users(self, exclude_view_only=True):
        """Get count of users that count toward subscription limits.

        Args:
            exclude_view_only: If True (default), exclude users with view-only roles

        Returns:
            int: Count of billable users
        """
        return self.get_billable_users_queryset(exclude_view_only=exclude_view_only).count()

    def get_subscription_limits(self):
        """Get subscription limits based on tier."""
        # Platform operators have unlimited access
        if self.is_platform_operator:
            return {
                "max_users": -1,  # Unlimited
                "max_projects": -1,  # Unlimited
                "max_storage_gb": -1,  # Unlimited
                "features": [
                    "basic_projects",
                    "basic_reports",
                    "advanced_reports",
                    "api_access",
                    "priority_support",
                    "custom_integrations",
                    "dedicated_support",
                    "sso",
                    "platform_administration",
                    "cross_org_support",
                ],
            }

        limits = {
            self.SUBSCRIPTION_FREE: {
                "max_users": 5,
                "max_projects": 3,
                "max_storage_gb": 1,
                "features": ["basic_projects", "basic_reports"],
            },
            self.SUBSCRIPTION_PRO: {
                "max_users": 50,
                "max_projects": 50,
                "max_storage_gb": 100,
                "features": [
                    "basic_projects",
                    "basic_reports",
                    "advanced_reports",
                    "api_access",
                    "priority_support",
                ],
            },
            self.SUBSCRIPTION_ENTERPRISE: {
                "max_users": -1,  # Unlimited
                "max_projects": -1,  # Unlimited
                "max_storage_gb": -1,  # Unlimited
                "features": [
                    "basic_projects",
                    "basic_reports",
                    "advanced_reports",
                    "api_access",
                    "priority_support",
                    "custom_branding",
                    "dedicated_support",
                    "sso",
                ],
            },
        }
        return limits.get(self.subscription_tier, limits[self.SUBSCRIPTION_FREE])

    def update_settings(self, key, value):
        """Update a specific setting for the organization."""
        if self.settings is None:
            self.settings = {}
        self.settings[key] = value
        self.save(update_fields=["settings", "updated_at"])
        return self.settings

    def get_setting(self, key, default=None):
        """Get a specific setting value."""
        if self.settings is None:
            return default
        return self.settings.get(key, default)

    def check_feature_access(self, feature_name):
        """Check if the organization has access to a specific feature."""
        limits = self.get_subscription_limits()
        return feature_name in limits.get("features", [])


class Role(models.Model):
    """Role model for role-based access control (RBAC)."""

    ROLE_LEVELS = (
        (10, _("Executive")),  # Higher authority (lower number)
        (20, _("Department Manager")),
        (30, _("Utility Coordinator")),
        (40, _("Stakeholder")),  # Lower authority (higher number)
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name="roles")
    name = models.CharField(_("name"), max_length=100)
    slug = models.SlugField(_("slug"), max_length=100)
    level = models.PositiveIntegerField(_("level"), choices=ROLE_LEVELS)
    permissions = models.JSONField(_("permissions"), default=dict)
    is_system_role = models.BooleanField(_("system role"), default=False)
    is_view_only = models.BooleanField(
        _("view only"),
        default=False,
        help_text=_("Whether this role only has read/view permissions"),
    )
    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        verbose_name = _("role")
        verbose_name_plural = _("roles")
        ordering = ["level", "name"]
        unique_together = [
            ["organization", "name"],
            ["organization", "slug"],
        ]
        indexes = [
            models.Index(fields=["organization", "level"]),
            models.Index(fields=["organization", "slug"]),
            models.Index(fields=["level"]),
            models.Index(fields=["is_system_role"]),
        ]

    def __str__(self):
        return f"{self.organization.name} - {self.name}"

    def save(self, *args, **kwargs):
        """Auto-generate slug if not provided."""
        if not self.slug:
            from django.utils.text import slugify

            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def is_billable(self):
        """Check if users with this role should be counted as billable."""
        return not self.is_view_only

    def has_write_permissions(self):
        """Check if this role has write/modify permissions."""
        return not self.is_view_only


class UserRoleManager(models.Manager):
    """Manager for UserRole with audit logging capabilities."""

    def assign_role(
        self,
        user,
        role,
        organization,
        assigned_by=None,
        is_primary=False,
        expires_at=None,
        custom_permissions=None,
        request=None,
    ):
        """
        Assign a role to a user with audit logging.

        Args:
            user: User to assign the role to
            role: Role to assign
            organization: Organization context
            assigned_by: User who is assigning the role
            is_primary: Whether this is the primary role
            expires_at: When the role assignment expires
            custom_permissions: Additional custom permissions
            request: HTTP request for capturing IP/user agent

        Returns:
            UserRole: Created or updated UserRole instance
        """
        from django.utils import timezone

        # Check if this role assignment already exists
        user_role, created = self.get_or_create(
            user=user,
            role=role,
            organization=organization,
            defaults={
                "assigned_by": assigned_by,
                "assigned_at": timezone.now(),
                "is_primary": is_primary,
                "expires_at": expires_at,
                "custom_permissions": custom_permissions or {},
            },
        )

        if not created:
            # Update existing role assignment
            user_role.assigned_by = assigned_by
            user_role.assigned_at = timezone.now()
            if expires_at is not None:
                user_role.expires_at = expires_at
            if custom_permissions is not None:
                user_role.custom_permissions = custom_permissions
            user_role.save()

        # Create audit log entry using PermissionAuditLog
        metadata = {
            "role_id": str(role.id),
            "role_name": role.name,
            "role_level": role.level,
            "is_primary": is_primary,
            "action": "created" if created else "updated",
        }

        if expires_at:
            metadata["expires_at"] = expires_at.isoformat()

        # Use PermissionAuditLog for role assignment logging (avoid circular import)
        # This will be called after the model is fully loaded
        def _log_permission_audit():
            PermissionAuditLog.log_role_change(
                affected_user=user,
                organization=organization,
                new_role=role,
                assigned_by=assigned_by,
                request=request,
                metadata=metadata,
            )

        # Schedule the audit logging to happen after model creation
        try:
            _log_permission_audit()
        except Exception:
            # If PermissionAuditLog is not available yet, skip for now
            pass

        # Also log to ProjectAuditTrail for backward compatibility
        ProjectAuditTrail.log_action(
            action_type="permission_granted",
            user=assigned_by,
            organization=organization,
            description=f"Assigned role '{role.name}' to user '{user.email}' in organization '{organization.name}'",
            severity="info",
            content_object=user_role,
            ip_address=request.META.get("REMOTE_ADDR") if request else None,
            user_agent=request.META.get("HTTP_USER_AGENT") if request else None,
            metadata=metadata,
            is_successful=True,
        )

        # If this is set as primary, ensure no other roles are primary
        if is_primary:
            self.filter(user=user).exclude(id=user_role.id).update(is_primary=False)

        return user_role

    def remove_role(self, user, role, organization, removed_by=None, reason=None, request=None):
        """
        Remove a role from a user with audit logging.

        Args:
            user: User to remove the role from
            role: Role to remove
            organization: Organization context
            removed_by: User who is removing the role
            reason: Reason for role removal
            request: HTTP request for capturing IP/user agent

        Returns:
            bool: True if role was removed, False if role didn't exist
        """
        try:
            user_role = self.get(user=user, role=role, organization=organization)

            # Store role info before deletion for audit log
            role_info = {
                "role_id": str(role.id),
                "role_name": role.name,
                "role_level": role.level,
                "was_primary": user_role.is_primary,
                "assigned_by": (str(user_role.assigned_by.id) if user_role.assigned_by else None),
                "assigned_at": (user_role.assigned_at.isoformat() if user_role.assigned_at else None),
                "removal_reason": reason,
            }

            # Delete the role assignment
            user_role.delete()

            # Create audit log entry using PermissionAuditLog (avoid circular import)
            def _log_permission_audit():
                PermissionAuditLog.log_permission_event(
                    event_type="role_removed",
                    organization=organization,
                    user=removed_by,
                    affected_user=user,
                    description=f"Removed role '{role.name}' from user '{user.email}' in organization '{organization.name}'",
                    severity="warning",
                    role=role,
                    request=request,
                    metadata=role_info,
                )

            try:
                _log_permission_audit()
            except Exception:
                # If PermissionAuditLog is not available yet, skip for now
                pass

            # Also log to ProjectAuditTrail for backward compatibility
            ProjectAuditTrail.log_action(
                action_type="permission_revoked",
                user=removed_by,
                organization=organization,
                description=f"Removed role '{role.name}' from user '{user.email}' in organization '{organization.name}'",
                severity="warning",
                ip_address=request.META.get("REMOTE_ADDR") if request else None,
                user_agent=request.META.get("HTTP_USER_AGENT") if request else None,
                metadata=role_info,
                is_successful=True,
            )

            # If this was the primary role, set another role as primary if available
            if role_info["was_primary"]:
                next_role = self.filter(user=user).first()
                if next_role:
                    next_role.is_primary = True
                    next_role.save()

            return True

        except UserRole.DoesNotExist:
            return False

    def get_active_roles(self, user, organization=None):
        """Get all active (non-expired) roles for a user."""
        from django.utils import timezone

        queryset = self.filter(user=user)
        if organization:
            queryset = queryset.filter(organization=organization)

        # Exclude expired roles
        queryset = queryset.filter(models.Q(expires_at__isnull=True) | models.Q(expires_at__gt=timezone.now()))

        return queryset.select_related("role", "organization")

    def bulk_assign_roles(self, users, role, organization, assigned_by=None, request=None):
        """
        Assign the same role to multiple users.

        Args:
            users: List of users to assign the role to
            role: Role to assign
            organization: Organization context
            assigned_by: User who is assigning the roles
            request: HTTP request for capturing IP/user agent

        Returns:
            list: List of created UserRole instances
        """
        created_roles = []

        for user in users:
            user_role = self.assign_role(
                user=user,
                role=role,
                organization=organization,
                assigned_by=assigned_by,
                is_primary=False,  # Bulk assignments are typically not primary
                request=request,
            )
            created_roles.append(user_role)

        return created_roles


class UserRole(models.Model):
    """User-Role-Organization relationship for multi-tenant RBAC."""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="user_roles")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name="user_roles")
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name="user_roles")

    # Assignment tracking
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="roles_assigned",
        help_text=_("User who assigned this role"),
    )
    assigned_at = models.DateTimeField(
        _("assigned at"),
        null=True,
        blank=True,
        help_text=_("When this role was assigned"),
    )

    # Role configuration
    custom_permissions = models.JSONField(_("custom permissions"), default=dict, blank=True)
    is_primary = models.BooleanField(_("primary role"), default=False)
    expires_at = models.DateTimeField(_("expires at"), null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)

    objects = UserRoleManager()

    class Meta:
        verbose_name = _("user role")
        verbose_name_plural = _("user roles")
        unique_together = ["user", "role", "organization"]
        ordering = ["organization", "role__level"]
        indexes = [
            models.Index(fields=["user", "organization"]),
            models.Index(fields=["role", "organization"]),
            models.Index(fields=["is_primary"]),
            models.Index(fields=["expires_at"]),
            models.Index(fields=["assigned_by", "-assigned_at"]),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.role.name} - {self.organization.name}"

    def is_active(self):
        """Check if this role assignment is currently active (not expired)."""
        if not self.expires_at:
            return True
        from django.utils import timezone

        return timezone.now() <= self.expires_at

    def save(self, *args, **kwargs):
        """Override save to set assigned_at if not provided."""
        if not self.assigned_at:
            from django.utils import timezone

            self.assigned_at = timezone.now()
        super().save(*args, **kwargs)


class UserProfile(models.Model):
    """Extended user profile information."""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    bio = models.TextField(_("biography"), max_length=500, blank=True)
    location = models.CharField(_("location"), max_length=100, blank=True)
    birth_date = models.DateField(_("birth date"), null=True, blank=True)
    website = models.URLField(_("website"), max_length=200, blank=True, validators=[URLValidator()])
    github = models.CharField(_("GitHub username"), max_length=50, blank=True)
    linkedin = models.CharField(_("LinkedIn username"), max_length=50, blank=True)
    twitter = models.CharField(_("Twitter username"), max_length=50, blank=True)
    avatar = models.ImageField(_("avatar"), upload_to="avatars/", null=True, blank=True)
    timezone = models.CharField(_("timezone"), max_length=50, default="UTC")
    language = models.CharField(_("language"), max_length=10, default="en")
    theme = models.CharField(_("theme"), max_length=20, default="light")
    notification_preferences = models.JSONField(_("notification preferences"), default=dict, blank=True)
    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        verbose_name = _("user profile")
        verbose_name_plural = _("user profiles")
        indexes = [
            models.Index(fields=["user"]),
            models.Index(fields=["location"]),
            models.Index(fields=["timezone"]),
        ]

    def __str__(self):
        return f"{self.user.email} Profile"

    def get_full_name(self):
        """Get full name from user or profile"""
        user_name = self.user.get_full_name()
        if user_name:
            return user_name
        return self.user.email

    def get_display_name(self):
        """Get display name for UI"""
        return self.get_full_name()

    def get_full_location(self):
        """Get full location string"""
        return self.location

    def get_completion_percentage(self):
        """Calculate profile completion percentage."""
        fields_to_check = [
            "bio",
            "location",
            "birth_date",
            "website",
            "github",
            "linkedin",
            "twitter",
            "avatar",
        ]

        completed_fields = 0
        for field in fields_to_check:
            value = getattr(self, field)
            if value:
                completed_fields += 1

        # Calculate percentage
        return int((completed_fields / len(fields_to_check)) * 100)

    def get_completion_status(self):
        """Get profile completion status."""
        percentage = self.get_completion_percentage()
        if percentage == 100:
            return "complete"
        elif percentage >= 75:
            return "almost_complete"
        elif percentage >= 50:
            return "partial"
        else:
            return "incomplete"

    def get_missing_fields(self):
        """Get list of missing profile fields."""
        fields_info = {
            "bio": _("Biography"),
            "location": _("Location"),
            "birth_date": _("Birth Date"),
            "website": _("Website"),
            "github": _("GitHub Username"),
            "linkedin": _("LinkedIn Username"),
            "twitter": _("Twitter Username"),
            "avatar": _("Profile Picture"),
        }

        missing = []
        for field, label in fields_info.items():
            value = getattr(self, field)
            if not value:
                missing.append({"field": field, "label": label})

        return missing


class ProjectAuditTrail(models.Model):
    """Audit trail for project-related activities and security events"""

    ACTION_TYPES = [
        ("project_created", "Project Created"),
        ("project_updated", "Project Updated"),
        ("project_deleted", "Project Deleted"),
        ("project_archived", "Project Archived"),
        ("project_restored", "Project Restored"),
        ("member_added", "Member Added"),
        ("member_removed", "Member Removed"),
        ("member_role_changed", "Member Role Changed"),
        ("task_created", "Task Created"),
        ("task_updated", "Task Updated"),
        ("task_deleted", "Task Deleted"),
        ("task_assigned", "Task Assigned"),
        ("task_completed", "Task Completed"),
        ("document_uploaded", "Document Uploaded"),
        ("document_deleted", "Document Deleted"),
        ("document_shared", "Document Shared"),
        ("permission_granted", "Permission Granted"),
        ("permission_revoked", "Permission Revoked"),
        ("data_exported", "Data Exported"),
        ("data_imported", "Data Imported"),
        ("settings_changed", "Settings Changed"),
        ("security_event", "Security Event"),
        ("access_denied", "Access Denied"),
        ("login_attempt", "Login Attempt"),
        ("report_generated", "Report Generated"),
        ("report_scheduled", "Report Scheduled"),
        ("custom_action", "Custom Action"),
    ]

    SEVERITY_LEVELS = [
        ("info", "Information"),
        ("warning", "Warning"),
        ("error", "Error"),
        ("critical", "Critical"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Core audit information
    action_type = models.CharField(max_length=50, choices=ACTION_TYPES, db_index=True)
    description = models.TextField(help_text="Detailed description of the action")
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default="info", db_index=True)

    # User and organization context
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="project_audit_entries",
        help_text="User who performed the action",
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="project_audit_entries",
        help_text="Organization context for multi-tenant isolation",
    )

    # Project context
    project = models.ForeignKey(
        "projects.Project",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="audit_entries",
        help_text="Related project if applicable",
    )

    # Target object (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.SET_NULL, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey("content_type", "object_id")
    object_repr = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="String representation of the object at time of action",
    )

    # Request context
    ip_address = models.GenericIPAddressField(null=True, blank=True, help_text="IP address of the request")
    user_agent = models.TextField(null=True, blank=True, help_text="Browser/client user agent")
    session_key = models.CharField(max_length=255, null=True, blank=True, help_text="Session identifier")
    request_id = models.CharField(max_length=255, null=True, blank=True, help_text="Request tracking ID")

    # Additional metadata
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional action-specific data (before/after values, etc.)",
    )

    # Status and outcome
    is_successful = models.BooleanField(default=True, help_text="Whether the action completed successfully")
    error_message = models.TextField(null=True, blank=True, help_text="Error message if action failed")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        verbose_name = _("project audit trail")
        verbose_name_plural = _("project audit trails")
        indexes = [
            models.Index(fields=["organization", "-created_at"]),
            models.Index(fields=["project", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["action_type", "-created_at"]),
            models.Index(fields=["severity", "-created_at"]),
            models.Index(fields=["content_type", "object_id"]),
            models.Index(fields=["ip_address", "-created_at"]),
            models.Index(fields=["is_successful", "-created_at"]),
        ]
        ordering = ["-created_at"]

    def __str__(self):
        user_str = self.user.email if self.user else "System"
        project_str = f" on {self.project.name}" if self.project else ""
        return f"{self.action_type} by {user_str}{project_str} at {self.created_at}"

    def save(self, *args, **kwargs):
        """Override save to set object_repr if not provided"""
        if self.content_object and not self.object_repr:
            try:
                self.object_repr = str(self.content_object)[:255]
            except Exception:
                self.object_repr = f"{self.content_type.model} #{self.object_id}"

        super().save(*args, **kwargs)

    @classmethod
    def log_action(
        cls,
        action_type,
        user=None,
        organization=None,
        project=None,
        description="",
        severity="info",
        content_object=None,
        ip_address=None,
        user_agent=None,
        session_key=None,
        metadata=None,
        is_successful=True,
        error_message=None,
    ):
        """
        Convenience method to log project audit actions.

        Args:
            action_type: Type of action from ACTION_TYPES choices
            user: User performing the action (optional for system actions)
            organization: Organization context (required for multi-tenant isolation)
            project: Related project (optional)
            description: Detailed description of the action
            severity: Severity level from SEVERITY_LEVELS choices
            content_object: Target object of the action (optional)
            ip_address: IP address of the request (optional)
            user_agent: User agent string (optional)
            session_key: Session identifier (optional)
            metadata: Additional action-specific data (optional)
            is_successful: Whether the action was successful
            error_message: Error message if action failed (optional)

        Returns:
            ProjectAuditTrail: Created audit entry
        """
        # Ensure organization is provided
        if not organization and user:
            # Try to get organization from user's primary role
            try:
                user_role = user.user_roles.filter(is_primary=True).first()
                if user_role:
                    organization = user_role.organization
            except Exception:
                pass

        if not organization:
            raise ValueError("Organization is required for audit trail entries")

        audit_entry = cls.objects.create(
            action_type=action_type,
            description=description,
            severity=severity,
            user=user,
            organization=organization,
            project=project,
            content_object=content_object,
            ip_address=ip_address,
            user_agent=user_agent,
            session_key=session_key,
            metadata=metadata or {},
            is_successful=is_successful,
            error_message=error_message,
        )

        return audit_entry


class DataAccessPolicy(models.Model):
    """Data access policies for role-based data governance"""

    ENFORCEMENT_LEVELS = [
        ("strict", "Strict - Block access completely"),
        ("warn", "Warning - Log and allow"),
        ("audit", "Audit - Log only"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Basic policy information
    name = models.CharField(max_length=255, help_text="Policy name")
    description = models.TextField(blank=True, null=True, help_text="Policy description")

    # Organization context
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="data_access_policies",
        help_text="Organization this policy applies to",
    )

    # Policy configuration
    model_name = models.CharField(max_length=100, help_text="Django model name this policy applies to")
    policy_expression = models.TextField(help_text="Policy expression in Django ORM syntax")
    applies_to_roles = models.JSONField(default=list, help_text="List of role names this policy applies to")
    enforcement_level = models.CharField(max_length=20, choices=ENFORCEMENT_LEVELS, default="strict")

    # Status and metadata
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_data_policies",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("data access policy")
        verbose_name_plural = _("data access policies")
        indexes = [
            models.Index(fields=["organization", "model_name"]),
            models.Index(fields=["is_active", "enforcement_level"]),
        ]
        ordering = ["name"]

    def __str__(self):
        return f"{self.organization.name} - {self.name}"


class DataMaskingRule(models.Model):
    """Data masking rules for sensitive data protection"""

    MASKING_TYPES = [
        ("hash", "Hash - Replace with hash"),
        ("mask", "Mask - Replace with asterisks"),
        ("partial", "Partial - Show only first/last characters"),
        ("replace", "Replace - Replace with custom pattern"),
        ("encrypt", "Encrypt - Encrypt the value"),
        ("remove", "Remove - Remove the field completely"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Basic rule information
    name = models.CharField(max_length=255, help_text="Masking rule name")
    description = models.TextField(blank=True, null=True, help_text="Rule description")

    # Organization context
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="data_masking_rules",
        help_text="Organization this rule applies to",
    )

    # Rule configuration
    model_name = models.CharField(max_length=100, help_text="Django model name")
    field_name = models.CharField(max_length=100, help_text="Field name to mask")
    masking_type = models.CharField(max_length=20, choices=MASKING_TYPES, default="mask")
    masking_pattern = models.CharField(max_length=255, blank=True, null=True, help_text="Custom masking pattern")
    applies_to_roles = models.JSONField(default=list, help_text="List of role names this rule applies to")

    # Status and metadata
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_masking_rules",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("data masking rule")
        verbose_name_plural = _("data masking rules")
        indexes = [
            models.Index(fields=["organization", "model_name", "field_name"]),
            models.Index(fields=["is_active", "masking_type"]),
        ]
        ordering = ["name"]
        constraints = [
            models.UniqueConstraint(
                fields=["organization", "model_name", "field_name"],
                name="unique_masking_rule_per_field",
            )
        ]

    def __str__(self):
        return f"{self.organization.name} - {self.model_name}.{self.field_name}"


class SessionSecurity(models.Model):
    """Session security monitoring and risk assessment"""

    RISK_LEVELS = [
        ("low", "Low Risk"),
        ("medium", "Medium Risk"),
        ("high", "High Risk"),
        ("critical", "Critical Risk"),
    ]

    STATUS_CHOICES = [
        ("active", "Active"),
        ("terminated", "Terminated"),
        ("expired", "Expired"),
        ("suspicious", "Suspicious"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Session identification
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="security_sessions",
        help_text="User associated with this session",
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="security_sessions",
        help_text="Organization context",
    )
    session_key = models.CharField(max_length=255, help_text="Django session key")

    # Session details
    ip_address = models.GenericIPAddressField(help_text="Client IP address")
    user_agent = models.TextField(help_text="Browser/client user agent")
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)

    # Security assessment
    risk_level = models.CharField(max_length=20, choices=RISK_LEVELS, default="low")
    risk_score = models.IntegerField(default=0, help_text="Calculated risk score (0-100)")
    risk_factors = models.JSONField(default=list, help_text="List of identified risk factors")

    # Status and actions
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="active")
    terminated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="terminated_sessions",
        help_text="User who terminated this session",
    )
    terminated_at = models.DateTimeField(null=True, blank=True)
    termination_reason = models.TextField(blank=True, null=True)

    # Geographic and device information
    country = models.CharField(max_length=100, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    device_type = models.CharField(max_length=50, blank=True, null=True)
    browser = models.CharField(max_length=100, blank=True, null=True)

    # Security flags
    is_suspicious = models.BooleanField(default=False)
    is_monitored = models.BooleanField(default=False)
    anomaly_detected = models.BooleanField(default=False)

    class Meta:
        verbose_name = _("session security")
        verbose_name_plural = _("session security")
        indexes = [
            models.Index(fields=["organization", "-last_activity"]),
            models.Index(fields=["user", "-last_activity"]),
            models.Index(fields=["risk_level", "status"]),
            models.Index(fields=["is_suspicious", "-created_at"]),
            models.Index(fields=["session_key"]),
        ]
        ordering = ["-last_activity"]

    def __str__(self):
        return f"{self.user.email} - {self.ip_address} ({self.risk_level})"


class DataExportLog(models.Model):
    """Log of data exports for audit and compliance"""

    EXPORT_TYPES = [
        ("csv", "CSV Export"),
        ("excel", "Excel Export"),
        ("pdf", "PDF Export"),
        ("json", "JSON Export"),
        ("xml", "XML Export"),
        ("api", "API Export"),
    ]

    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("processing", "Processing"),
        ("completed", "Completed"),
        ("failed", "Failed"),
        ("cancelled", "Cancelled"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Export identification
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="data_exports",
        help_text="User who initiated the export",
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="data_exports",
        help_text="Organization context",
    )

    # Export details
    export_type = models.CharField(max_length=20, choices=EXPORT_TYPES)
    model_name = models.CharField(max_length=100, help_text="Model being exported")
    record_count = models.PositiveIntegerField(default=0, help_text="Number of records exported")
    file_size_bytes = models.PositiveIntegerField(null=True, blank=True)

    # Export parameters
    filters = models.JSONField(default=dict, help_text="Filters applied to the export")
    fields_exported = models.JSONField(default=list, help_text="List of fields included in export")

    # Status and timing
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    processing_time_seconds = models.PositiveIntegerField(null=True, blank=True)

    # Security and compliance
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True, null=True)
    justification = models.TextField(blank=True, null=True, help_text="Business justification for export")
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="approved_exports",
        help_text="User who approved this export",
    )

    # File information
    file_path = models.CharField(max_length=500, blank=True, null=True)
    download_count = models.PositiveIntegerField(default=0)
    last_downloaded = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    # Error handling
    error_message = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name = _("data export log")
        verbose_name_plural = _("data export logs")
        indexes = [
            models.Index(fields=["organization", "-started_at"]),
            models.Index(fields=["user", "-started_at"]),
            models.Index(fields=["status", "-started_at"]),
            models.Index(fields=["model_name", "-started_at"]),
            models.Index(fields=["export_type", "-started_at"]),
        ]
        ordering = ["-started_at"]

    def __str__(self):
        return f"{self.model_name} export by {self.user.email if self.user else 'System'} - {self.started_at}"


class DataExportRestriction(models.Model):
    """Restrictions on data exports for compliance and security"""

    RESTRICTION_TYPES = [
        ("role_based", "Role-based Restriction"),
        ("field_based", "Field-based Restriction"),
        ("time_based", "Time-based Restriction"),
        ("volume_based", "Volume-based Restriction"),
        ("approval_required", "Approval Required"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Basic restriction information
    name = models.CharField(max_length=255, help_text="Restriction name")
    description = models.TextField(blank=True, null=True, help_text="Restriction description")

    # Organization context
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="export_restrictions",
        help_text="Organization this restriction applies to",
    )

    # Restriction configuration
    restriction_type = models.CharField(max_length=20, choices=RESTRICTION_TYPES)
    model_name = models.CharField(max_length=100, help_text="Model this restriction applies to")
    restricted_fields = models.JSONField(default=list, help_text="List of restricted field names")
    applies_to_roles = models.JSONField(default=list, help_text="List of role names this restriction applies to")

    # Limits and conditions
    max_records_per_export = models.PositiveIntegerField(null=True, blank=True)
    max_exports_per_day = models.PositiveIntegerField(null=True, blank=True)
    requires_approval = models.BooleanField(default=False)
    approval_roles = models.JSONField(default=list, help_text="Roles that can approve exports")

    # Time-based restrictions
    allowed_hours_start = models.TimeField(null=True, blank=True)
    allowed_hours_end = models.TimeField(null=True, blank=True)
    allowed_days_of_week = models.JSONField(default=list, help_text="List of allowed days (0=Monday)")

    # Status and metadata
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_export_restrictions",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("data export restriction")
        verbose_name_plural = _("data export restrictions")
        indexes = [
            models.Index(fields=["organization", "model_name"]),
            models.Index(fields=["restriction_type", "is_active"]),
        ]
        ordering = ["name"]

    def __str__(self):
        return f"{self.organization.name} - {self.name}"


class OrganizationInvitation(models.Model):
    """Model for inviting users to join organizations with specific roles."""

    STATUS_CHOICES = [
        ("pending", _("Pending")),
        ("accepted", _("Accepted")),
        ("declined", _("Declined")),
        ("expired", _("Expired")),
        ("cancelled", _("Cancelled")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Invitation details
    email = models.EmailField(_("email address"), help_text="Email of the user being invited")
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="invitations",
        help_text="Organization the user is being invited to",
    )
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name="invitations",
        help_text="Role to assign to the user",
    )

    # Invitation metadata
    invited_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="sent_invitations",
        help_text="User who sent the invitation",
    )
    message = models.TextField(
        _("message"),
        blank=True,
        help_text="Optional message to include with the invitation",
    )

    # Status and tracking
    status = models.CharField(_("status"), max_length=20, choices=STATUS_CHOICES, default="pending")
    token = models.CharField(
        _("invitation token"),
        max_length=64,
        unique=True,
        help_text="Unique token for accepting the invitation",
    )

    # Timestamps
    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)
    expires_at = models.DateTimeField(_("expires at"), help_text="When this invitation expires")
    accepted_at = models.DateTimeField(_("accepted at"), null=True, blank=True)

    # User who accepted (might be different from invited email if user already exists)
    accepted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="accepted_invitations",
        help_text="User who accepted the invitation",
    )

    class Meta:
        verbose_name = _("organization invitation")
        verbose_name_plural = _("organization invitations")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["email", "status"]),
            models.Index(fields=["organization", "status"]),
            models.Index(fields=["token"]),
            models.Index(fields=["expires_at", "status"]),
        ]
        unique_together = [
            [
                "email",
                "organization",
                "status",
            ],  # Prevent duplicate pending invitations
        ]

    def __str__(self):
        return f"Invite {self.email} to {self.organization.name} as {self.role.name}"

    def save(self, *args, **kwargs):
        # Generate token if not set
        if not self.token:
            import secrets

            self.token = secrets.token_urlsafe(32)

        # Set expiration if not set (default to 7 days)
        if not self.expires_at:
            import datetime

            from django.utils import timezone

            self.expires_at = timezone.now() + datetime.timedelta(days=7)

        super().save(*args, **kwargs)

    def is_expired(self):
        """Check if the invitation has expired."""
        from django.utils import timezone

        return timezone.now() > self.expires_at

    def can_be_accepted(self):
        """Check if the invitation can still be accepted."""
        return self.status == "pending" and not self.is_expired()

    def accept(self, user):
        """Accept the invitation and create the UserRole."""
        if not self.can_be_accepted():
            raise ValueError("Invitation cannot be accepted")

        # Check if user already has this role in the organization
        existing_role = UserRole.objects.filter(user=user, organization=self.organization, role=self.role).first()

        if existing_role:
            # Update existing role if needed
            if not existing_role.is_primary and user.user_roles.count() == 0:
                existing_role.is_primary = True
                existing_role.save()
        else:
            # Use the UserRole manager to create new role with audit logging
            is_primary = user.user_roles.count() == 0  # First organization becomes primary
            UserRole.objects.assign_role(
                user=user,
                role=self.role,
                organization=self.organization,
                assigned_by=self.invited_by,  # The person who sent the invitation assigns the role
                is_primary=is_primary,
            )

        # Update invitation status
        from django.utils import timezone

        self.status = "accepted"
        self.accepted_by = user
        self.accepted_at = timezone.now()
        self.save()

        return True

    def decline(self):
        """Decline the invitation."""
        if not self.can_be_accepted():
            raise ValueError("Invitation cannot be declined")

        self.status = "declined"
        self.save()

    def cancel(self):
        """Cancel the invitation (only by inviter or org admin)."""
        if self.status != "pending":
            raise ValueError("Only pending invitations can be cancelled")

        self.status = "cancelled"
        self.save()


class AuthAuditLog(models.Model):
    """
    Audit log for authentication events.
    Tracks login attempts, password changes, and other security events.
    """

    EVENT_TYPES = [
        ("login_success", _("Login Success")),
        ("login_failure", _("Login Failure")),
        ("login_blocked", _("Login Blocked")),
        ("logout", _("Logout")),
        ("password_change", _("Password Change")),
        ("password_reset_request", _("Password Reset Request")),
        ("password_reset_complete", _("Password Reset Complete")),
        ("account_locked", _("Account Locked")),
        ("account_unlocked", _("Account Unlocked")),
        ("mfa_enabled", _("MFA Enabled")),
        ("mfa_disabled", _("MFA Disabled")),
        ("mfa_success", _("MFA Success")),
        ("mfa_failure", _("MFA Failure")),
        ("session_expired", _("Session Expired")),
        ("concurrent_session_limit", _("Concurrent Session Limit")),
        ("suspicious_activity", _("Suspicious Activity")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="auth_audit_logs",
    )
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)

    # Additional context
    username_attempted = models.CharField(max_length=150, blank=True)
    reason = models.TextField(blank=True)
    extra_data = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["event_type", "-created_at"]),
            models.Index(fields=["ip_address", "-created_at"]),
            models.Index(fields=["-created_at"]),
        ]
        verbose_name = _("Authentication Audit Log")
        verbose_name_plural = _("Authentication Audit Logs")

    def __str__(self):
        if self.user:
            return f"{self.get_event_type_display()} - {self.user.email} - {self.created_at}"
        return f"{self.get_event_type_display()} - {self.username_attempted} - {self.created_at}"

    @classmethod
    def log_event(
        cls,
        event_type,
        request,
        user=None,
        username_attempted=None,
        reason=None,
        extra_data=None,
    ):
        """
        Create an audit log entry.

        Args:
            event_type: One of the EVENT_TYPES choices
            request: The HTTP request object
            user: The user object (if authenticated)
            username_attempted: Username/email attempted (for failed logins)
            reason: Additional context or reason
            extra_data: Dict of additional data to store
        """
        ip_address = cls.get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        return cls.objects.create(
            user=user,
            event_type=event_type,
            ip_address=ip_address,
            user_agent=user_agent,
            username_attempted=username_attempted or "",
            reason=reason or "",
            extra_data=extra_data or {},
        )

    @staticmethod
    def get_client_ip(request):
        """Extract client IP from request, considering proxies."""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordHistory(models.Model):
    """
    Track password history to prevent reuse.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="password_history")
    password_hash = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "-created_at"]),
        ]

    def __str__(self):
        return f"Password history for {self.user.email} - {self.created_at}"


class PermissionAuditLog(models.Model):
    """
    Audit log for permission-related events.
    Tracks role changes, permission modifications, and access attempts.
    """

    PERMISSION_EVENT_TYPES = [
        ("role_assigned", _("Role Assigned")),
        ("role_removed", _("Role Removed")),
        ("role_modified", _("Role Modified")),
        ("permission_granted", _("Permission Granted")),
        ("permission_revoked", _("Permission Revoked")),
        ("permission_denied", _("Permission Denied")),
        ("access_granted", _("Access Granted")),
        ("access_denied", _("Access Denied")),
        ("object_permission_added", _("Object Permission Added")),
        ("object_permission_removed", _("Object Permission Removed")),
        ("bulk_role_assignment", _("Bulk Role Assignment")),
        ("role_expiry_updated", _("Role Expiry Updated")),
        ("custom_permission_added", _("Custom Permission Added")),
        ("custom_permission_removed", _("Custom Permission Removed")),
        ("organization_access_granted", _("Organization Access Granted")),
        ("organization_access_revoked", _("Organization Access Revoked")),
        ("permission_check", _("Permission Check")),
        ("security_violation", _("Security Violation")),
    ]

    SEVERITY_LEVELS = [
        ("info", _("Information")),
        ("warning", _("Warning")),
        ("error", _("Error")),
        ("critical", _("Critical")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Core event information
    event_type = models.CharField(max_length=50, choices=PERMISSION_EVENT_TYPES, db_index=True)
    description = models.TextField(help_text="Detailed description of the permission event")
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default="info", db_index=True)

    # User and organization context
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="auth_permission_audit_logs",
        help_text="User who performed the action or whose permissions were affected",
    )
    affected_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="affected_permission_logs",
        help_text="User whose permissions were changed (if different from actor)",
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="auth_permission_audit_logs",
        help_text="Organization context for multi-tenant isolation",
    )

    # Permission context
    permission_name = models.CharField(
        max_length=255,
        blank=True,
        help_text="Name of the permission being checked/modified",
    )
    object_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Type of object the permission applies to",
    )
    object_id = models.PositiveIntegerField(null=True, blank=True, help_text="ID of the specific object")
    content_object = GenericForeignKey("object_type", "object_id")

    # Role context
    role = models.ForeignKey(
        Role,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="permission_audit_logs",
        help_text="Role involved in the permission event",
    )
    previous_role = models.ForeignKey(
        Role,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="previous_role_audit_logs",
        help_text="Previous role (for role changes)",
    )

    # Request context
    ip_address = models.GenericIPAddressField(null=True, blank=True, help_text="IP address of the request")
    user_agent = models.TextField(null=True, blank=True, help_text="Browser/client user agent")
    session_key = models.CharField(max_length=255, null=True, blank=True, help_text="Session identifier")
    request_path = models.CharField(
        max_length=500,
        blank=True,
        help_text="URL path that triggered the permission check",
    )

    # Additional metadata
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional event-specific data (before/after values, etc.)",
    )

    # Status and outcome
    is_successful = models.BooleanField(default=True, help_text="Whether the permission action was successful")
    denial_reason = models.TextField(null=True, blank=True, help_text="Reason for permission denial")

    # Compliance and audit tracking
    compliance_tags = models.JSONField(
        default=list,
        blank=True,
        help_text="Tags for compliance reporting (e.g., GDPR, SOX, HIPAA)",
    )
    audit_reference = models.CharField(max_length=100, blank=True, help_text="External audit reference number")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        verbose_name = _("permission audit log")
        verbose_name_plural = _("permission audit logs")
        indexes = [
            models.Index(fields=["organization", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["affected_user", "-created_at"]),
            models.Index(fields=["event_type", "-created_at"]),
            models.Index(fields=["severity", "-created_at"]),
            models.Index(fields=["permission_name", "-created_at"]),
            models.Index(fields=["role", "-created_at"]),
            models.Index(fields=["object_type", "object_id"]),
            models.Index(fields=["ip_address", "-created_at"]),
            models.Index(fields=["is_successful", "-created_at"]),
            models.Index(fields=["compliance_tags"]),
        ]
        ordering = ["-created_at"]

    def __str__(self):
        user_str = self.user.email if self.user else "System"
        affected_str = (
            f" affecting {self.affected_user.email}" if self.affected_user and self.affected_user != self.user else ""
        )
        return f"{self.event_type} by {user_str}{affected_str} at {self.created_at}"

    @classmethod
    def log_permission_event(
        cls,
        event_type,
        organization,
        user=None,
        affected_user=None,
        description="",
        severity="info",
        permission_name=None,
        content_object=None,
        role=None,
        previous_role=None,
        request=None,
        is_successful=True,
        denial_reason=None,
        metadata=None,
        compliance_tags=None,
        audit_reference=None,
    ):
        """
        Convenience method to log permission events.

        Args:
            event_type: Type of permission event from PERMISSION_EVENT_TYPES
            organization: Organization context (required)
            user: User performing the action (optional for system actions)
            affected_user: User whose permissions are affected (optional)
            description: Detailed description of the event
            severity: Severity level from SEVERITY_LEVELS
            permission_name: Name of the permission being checked/modified
            content_object: Target object of the permission (optional)
            role: Role involved in the event (optional)
            previous_role: Previous role for role changes (optional)
            request: HTTP request object for context (optional)
            is_successful: Whether the permission action was successful
            denial_reason: Reason for permission denial (optional)
            metadata: Additional event-specific data (optional)
            compliance_tags: Tags for compliance reporting (optional)
            audit_reference: External audit reference (optional)

        Returns:
            PermissionAuditLog: Created audit entry
        """
        # Extract request context if provided
        ip_address = None
        user_agent = None
        session_key = None
        request_path = None

        if request:
            ip_address = cls._get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")
            session_key = request.session.session_key if hasattr(request, "session") else None
            request_path = request.path if hasattr(request, "path") else None

        audit_entry = cls.objects.create(
            event_type=event_type,
            description=description,
            severity=severity,
            user=user,
            affected_user=affected_user or user,
            organization=organization,
            permission_name=permission_name or "",
            content_object=content_object,
            role=role,
            previous_role=previous_role,
            ip_address=ip_address,
            user_agent=user_agent,
            session_key=session_key,
            request_path=request_path,
            metadata=metadata or {},
            is_successful=is_successful,
            denial_reason=denial_reason,
            compliance_tags=compliance_tags or [],
            audit_reference=audit_reference or "",
        )

        return audit_entry

    @classmethod
    def log_access_attempt(
        cls,
        permission_name,
        user,
        organization,
        content_object=None,
        request=None,
        is_granted=True,
        denial_reason=None,
    ):
        """
        Log an access attempt (permission check).

        Args:
            permission_name: Name of the permission being checked
            user: User attempting access
            organization: Organization context
            content_object: Object being accessed (optional)
            request: HTTP request object (optional)
            is_granted: Whether access was granted
            denial_reason: Reason for denial if access was denied
        """
        event_type = "access_granted" if is_granted else "access_denied"
        severity = "info" if is_granted else "warning"

        description = f"Access {'granted' if is_granted else 'denied'} for permission '{permission_name}'"
        if content_object:
            description += f" on {content_object}"

        return cls.log_permission_event(
            event_type=event_type,
            organization=organization,
            user=user,
            description=description,
            severity=severity,
            permission_name=permission_name,
            content_object=content_object,
            request=request,
            is_successful=is_granted,
            denial_reason=denial_reason,
        )

    @classmethod
    def log_role_change(
        cls,
        affected_user,
        organization,
        new_role,
        previous_role=None,
        assigned_by=None,
        request=None,
        metadata=None,
    ):
        """
        Log a role assignment or change.

        Args:
            affected_user: User whose role is being changed
            organization: Organization context
            new_role: New role being assigned
            previous_role: Previous role (if changing, not just adding)
            assigned_by: User performing the role assignment
            request: HTTP request object (optional)
            metadata: Additional metadata about the change
        """
        if previous_role:
            event_type = "role_modified"
            description = (
                f"Role changed from '{previous_role.name}' to '{new_role.name}' for user '{affected_user.email}'"
            )
        else:
            event_type = "role_assigned"
            description = f"Role '{new_role.name}' assigned to user '{affected_user.email}'"

        return cls.log_permission_event(
            event_type=event_type,
            organization=organization,
            user=assigned_by,
            affected_user=affected_user,
            description=description,
            severity="info",
            role=new_role,
            previous_role=previous_role,
            request=request,
            metadata=metadata,
        )

    @classmethod
    def log_permission_denial(
        cls,
        permission_name,
        user,
        organization,
        content_object=None,
        request=None,
        denial_reason=None,
        metadata=None,
    ):
        """
        Log a permission denial for security monitoring.

        Args:
            permission_name: Name of the permission that was denied
            user: User who was denied access
            organization: Organization context
            content_object: Object being accessed (optional)
            request: HTTP request object (optional)
            denial_reason: Reason for the denial
            metadata: Additional context about the denial
        """
        return cls.log_permission_event(
            event_type="permission_denied",
            organization=organization,
            user=user,
            description=f"Permission '{permission_name}' denied for user '{user.email}'",
            severity="warning",
            permission_name=permission_name,
            content_object=content_object,
            request=request,
            is_successful=False,
            denial_reason=denial_reason,
            metadata=metadata,
        )

    @staticmethod
    def _get_client_ip(request):
        """Extract client IP from request, considering proxies."""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class SecurityEvent(models.Model):
    """
    Security events for analytics and monitoring.
    Tracks various security-related incidents and patterns.
    """

    EVENT_TYPES = [
        ("login_attempt", _("Login Attempt")),
        ("failed_login", _("Failed Login")),
        ("account_lockout", _("Account Lockout")),
        ("password_reset", _("Password Reset")),
        ("mfa_bypass_attempt", _("MFA Bypass Attempt")),
        ("suspicious_activity", _("Suspicious Activity")),
        ("data_access_violation", _("Data Access Violation")),
        ("privilege_escalation", _("Privilege Escalation")),
        ("session_hijacking", _("Session Hijacking")),
        ("brute_force_attack", _("Brute Force Attack")),
        ("sql_injection_attempt", _("SQL Injection Attempt")),
        ("xss_attempt", _("XSS Attempt")),
        ("csrf_attack", _("CSRF Attack")),
        ("rate_limit_exceeded", _("Rate Limit Exceeded")),
        ("geolocation_anomaly", _("Geolocation Anomaly")),
        ("device_anomaly", _("Device Anomaly")),
        ("time_anomaly", _("Time Anomaly")),
        ("permission_denied", _("Permission Denied")),
        ("unauthorized_access", _("Unauthorized Access")),
        ("data_export_violation", _("Data Export Violation")),
    ]

    SEVERITY_LEVELS = [
        ("info", _("Information")),
        ("low", _("Low")),
        ("medium", _("Medium")),
        ("high", _("High")),
        ("critical", _("Critical")),
    ]

    STATUS_CHOICES = [
        ("open", _("Open")),
        ("investigating", _("Investigating")),
        ("resolved", _("Resolved")),
        ("false_positive", _("False Positive")),
        ("suppressed", _("Suppressed")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Event details
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES, db_index=True)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default="info")
    title = models.CharField(max_length=255, help_text="Brief event title")
    description = models.TextField(help_text="Detailed event description")

    # Context
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="security_events",
        help_text="User associated with this event",
    )
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="security_events", help_text="Organization context"
    )

    # Request context
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    session_key = models.CharField(max_length=255, blank=True)
    request_path = models.CharField(max_length=500, blank=True)
    request_method = models.CharField(max_length=10, blank=True)

    # Geographic context
    country = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100, blank=True)
    timezone = models.CharField(max_length=50, blank=True)

    # Technical details
    risk_score = models.IntegerField(default=0, help_text="Calculated risk score (0-100)")
    metadata = models.JSONField(default=dict, blank=True, help_text="Additional event-specific data")

    # Status and resolution
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="open")
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_security_events",
        help_text="User assigned to investigate this event",
    )
    resolution_notes = models.TextField(blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    # Related events
    parent_event = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="child_events",
        help_text="Parent event if this is part of a chain",
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("security event")
        verbose_name_plural = _("security events")
        indexes = [
            models.Index(fields=["organization", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["event_type", "-created_at"]),
            models.Index(fields=["severity", "status"]),
            models.Index(fields=["risk_score", "-created_at"]),
            models.Index(fields=["ip_address", "-created_at"]),
            models.Index(fields=["status", "-created_at"]),
        ]
        ordering = ["-created_at"]

    def __str__(self):
        user_str = self.user.email if self.user else "Unknown"
        return f"{self.get_event_type_display()} - {user_str} - {self.created_at}"

    @classmethod
    def log_event(
        cls,
        event_type,
        title,
        description,
        organization,
        severity="info",
        user=None,
        request=None,
        risk_score=0,
        metadata=None,
        parent_event=None,
    ):
        """
        Create a security event log entry.

        Args:
            event_type: Type of security event
            title: Brief event title
            description: Detailed description
            organization: Organization context
            severity: Severity level
            user: Associated user (optional)
            request: HTTP request object (optional)
            risk_score: Calculated risk score (0-100)
            metadata: Additional event data
            parent_event: Parent event if this is part of a chain

        Returns:
            SecurityEvent: Created security event
        """
        # Extract request context if provided
        ip_address = None
        user_agent = ""
        session_key = ""
        request_path = ""
        request_method = ""

        if request:
            ip_address = cls._get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")
            session_key = request.session.session_key if hasattr(request, "session") else ""
            request_path = request.path if hasattr(request, "path") else ""
            request_method = request.method if hasattr(request, "method") else ""

        event = cls.objects.create(
            event_type=event_type,
            severity=severity,
            title=title,
            description=description,
            user=user,
            organization=organization,
            ip_address=ip_address,
            user_agent=user_agent,
            session_key=session_key,
            request_path=request_path,
            request_method=request_method,
            risk_score=risk_score,
            metadata=metadata or {},
            parent_event=parent_event,
        )

        return event

    @staticmethod
    def _get_client_ip(request):
        """Extract client IP from request, considering proxies."""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


# Import preference models to make them available
from .models_preferences import UserPreference, UserPreferenceTemplate

# Make sure Django detects the models by adding them to __all__
__all__ = [
    "User",
    "Organization",
    "Role",
    "UserRole",
    "UserProfile",
    "ProjectAuditTrail",
    "DataAccessPolicy",
    "DataMaskingRule",
    "SessionSecurity",
    "DataExportLog",
    "DataExportRestriction",
    "OrganizationInvitation",
    "AuthAuditLog",
    "PasswordHistory",
    "PermissionAuditLog",
    "SecurityEvent",
    "UserPreference",
    "UserPreferenceTemplate",
]
