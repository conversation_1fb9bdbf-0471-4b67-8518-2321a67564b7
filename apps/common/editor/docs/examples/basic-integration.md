# Basic Integration Examples

Simple examples for integrating the markdown editor into your Django applications.

## Basic Form Integration

### Simple Form

```python
# forms.py
from django import forms
from apps.common.editor.widgets import MarkdownEditorFormField

class BlogPostForm(forms.Form):
    title = forms.CharField(max_length=200)
    content = MarkdownEditorFormField(
        context='professional',
        help_text='Write your blog post using markdown formatting'
    )
    
    def clean_content(self):
        content = self.cleaned_data['content']
        if len(content) < 100:
            raise forms.ValidationError('Content must be at least 100 characters')
        return content
```

### Template Usage

```html
<!-- templates/blog/create_post.html -->
{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'css/markdown-editor.css' %}">
{% endblock %}

{% block content %}
<div class="container">
    <h1>Create Blog Post</h1>
    
    <form method="post">
        {% csrf_token %}
        
        <div class="form-group">
            <label for="{{ form.title.id_for_label }}">{{ form.title.label }}</label>
            {{ form.title }}
            {% if form.title.errors %}
                <div class="text-danger">{{ form.title.errors.0 }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.content.id_for_label }}">{{ form.content.label }}</label>
            {{ form.content }}
            {% if form.content.help_text %}
                <small class="form-text text-muted">{{ form.content.help_text }}</small>
            {% endif %}
            {% if form.content.errors %}
                <div class="text-danger">{{ form.content.errors.0 }}</div>
            {% endif %}
        </div>
        
        <button type="submit" class="btn btn-primary">Create Post</button>
    </form>
</div>
{% endblock %}

{% block extra_js %}
    <script src="{% static 'js/controllers/markdown_editor_controller.js' %}"></script>
{% endblock %}
```

### View Implementation

```python
# views.py
from django.shortcuts import render, redirect
from django.contrib import messages
from .forms import BlogPostForm
from .models import BlogPost

def create_blog_post(request):
    if request.method == 'POST':
        form = BlogPostForm(request.POST)
        if form.is_valid():
            post = BlogPost.objects.create(
                title=form.cleaned_data['title'],
                content=form.cleaned_data['content'],
                author=request.user
            )
            messages.success(request, 'Blog post created successfully!')
            return redirect('blog:detail', pk=post.pk)
    else:
        form = BlogPostForm()
    
    return render(request, 'blog/create_post.html', {'form': form})
```

## Model Integration

### Model with Markdown Field

```python
# models.py
from django.db import models
from django.contrib.auth.models import User

class BlogPost(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title
    
    def get_content_preview(self, length=150):
        """Return a preview of the content."""
        if len(self.content) <= length:
            return self.content
        return self.content[:length] + '...'

class Comment(models.Model):
    post = models.ForeignKey(BlogPost, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['created_at']
```

### ModelForm Integration

```python
# forms.py
from django import forms
from apps.common.editor.widgets import MarkdownEditorFormField
from .models import BlogPost, Comment

class BlogPostForm(forms.ModelForm):
    content = MarkdownEditorFormField(
        context='professional',
        help_text='Use markdown formatting for rich content'
    )
    
    class Meta:
        model = BlogPost
        fields = ['title', 'content']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'})
        }

class CommentForm(forms.ModelForm):
    content = MarkdownEditorFormField(
        context='comment',
        help_text='Share your thoughts (markdown supported)'
    )
    
    class Meta:
        model = Comment
        fields = ['content']
```

## Template Tag Usage

### Custom Template Tags

```python
# templatetags/editor_tags.py
from django import template
from django.utils.safestring import mark_safe
from apps.common.editor.widgets import MarkdownEditorWidget

register = template.Library()

@register.inclusion_tag('editor/inline_editor.html')
def inline_markdown_editor(field_name, initial_value='', context='minimal', **kwargs):
    """Render an inline markdown editor."""
    widget = MarkdownEditorWidget(context=context)
    return {
        'widget_html': widget.render(field_name, initial_value, kwargs),
        'field_name': field_name,
        'initial_value': initial_value,
        'context': context
    }

@register.simple_tag
def markdown_editor_assets():
    """Include required CSS and JS for markdown editor."""
    return mark_safe('''
        <link rel="stylesheet" href="/static/css/markdown-editor.css">
        <script src="/static/js/controllers/markdown_editor_controller.js"></script>
    ''')
```

### Template Usage

```html
<!-- templates/editor/inline_editor.html -->
<div class="inline-editor">
    {{ widget_html|safe }}
</div>

<!-- templates/blog/detail.html -->
{% load editor_tags %}

<!DOCTYPE html>
<html>
<head>
    <title>{{ post.title }}</title>
    {% markdown_editor_assets %}
</head>
<body>
    <article>
        <h1>{{ post.title }}</h1>
        <div class="content">
            {{ post.content|linebreaks }}
        </div>
    </article>
    
    <!-- Quick edit form -->
    {% if user == post.author %}
        <div class="quick-edit">
            <h3>Quick Edit</h3>
            {% inline_markdown_editor 'content' post.content 'professional' %}
        </div>
    {% endif %}
    
    <!-- Comments section -->
    <div class="comments">
        <h3>Comments</h3>
        {% for comment in post.comments.all %}
            <div class="comment">
                <div class="author">{{ comment.author.username }}</div>
                <div class="content">{{ comment.content|linebreaks }}</div>
                <div class="date">{{ comment.created_at }}</div>
            </div>
        {% endfor %}
        
        <!-- Add comment form -->
        {% if user.is_authenticated %}
            <form method="post" action="{% url 'blog:add_comment' post.pk %}">
                {% csrf_token %}
                {% inline_markdown_editor 'content' '' 'comment' placeholder='Add your comment...' %}
                <button type="submit">Add Comment</button>
            </form>
        {% endif %}
    </div>
</body>
</html>
```

## AJAX Integration

### AJAX Form Submission

```html
<!-- templates/blog/ajax_form.html -->
<form id="post-form" method="post">
    {% csrf_token %}
    <div class="form-group">
        <label>Title</label>
        <input type="text" name="title" class="form-control" required>
    </div>
    
    <div class="form-group">
        <label>Content</label>
        <textarea 
            name="content" 
            data-controller="markdown-editor"
            data-markdown-editor-context-value="professional"
            data-markdown-editor-auto-save-value="true"
            class="form-control"
            rows="10">
        </textarea>
    </div>
    
    <button type="submit" class="btn btn-primary">Save Post</button>
</form>

<script>
document.getElementById('post-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    try {
        const response = await fetch('/api/posts/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        if (response.ok) {
            const result = await response.json();
            alert('Post saved successfully!');
            window.location.href = `/posts/${result.id}/`;
        } else {
            const errors = await response.json();
            displayErrors(errors);
        }
    } catch (error) {
        console.error('Error saving post:', error);
        alert('Error saving post. Please try again.');
    }
});

function displayErrors(errors) {
    // Clear previous errors
    document.querySelectorAll('.error-message').forEach(el => el.remove());
    
    // Display new errors
    for (const [field, messages] of Object.entries(errors)) {
        const fieldElement = document.querySelector(`[name="${field}"]`);
        if (fieldElement) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message text-danger';
            errorDiv.textContent = messages.join(', ');
            fieldElement.parentNode.appendChild(errorDiv);
        }
    }
}
</script>
```

## API Integration

### Django REST Framework

```python
# serializers.py
from rest_framework import serializers
from .models import BlogPost, Comment

class BlogPostSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.username', read_only=True)
    content_preview = serializers.CharField(read_only=True)
    
    class Meta:
        model = BlogPost
        fields = ['id', 'title', 'content', 'author', 'author_name', 
                 'content_preview', 'created_at', 'updated_at']
        read_only_fields = ['author', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        validated_data['author'] = self.context['request'].user
        return super().create(validated_data)

class CommentSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.username', read_only=True)
    
    class Meta:
        model = Comment
        fields = ['id', 'content', 'author', 'author_name', 'created_at']
        read_only_fields = ['author', 'created_at']

# views.py
from rest_framework import viewsets, permissions
from .models import BlogPost, Comment
from .serializers import BlogPostSerializer, CommentSerializer

class BlogPostViewSet(viewsets.ModelViewSet):
    queryset = BlogPost.objects.all()
    serializer_class = BlogPostSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def perform_create(self, serializer):
        serializer.save(author=self.request.user)

class CommentViewSet(viewsets.ModelViewSet):
    serializer_class = CommentSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        return Comment.objects.filter(post_id=self.kwargs['post_pk'])
    
    def perform_create(self, serializer):
        post = BlogPost.objects.get(pk=self.kwargs['post_pk'])
        serializer.save(author=self.request.user, post=post)
```

### Frontend API Usage

```javascript
// static/js/api-client.js
class BlogAPIClient {
    constructor() {
        this.baseURL = '/api';
        this.csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    }
    
    async createPost(postData) {
        const response = await fetch(`${this.baseURL}/posts/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.csrfToken
            },
            body: JSON.stringify(postData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return response.json();
    }
    
    async updatePost(id, postData) {
        const response = await fetch(`${this.baseURL}/posts/${id}/`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.csrfToken
            },
            body: JSON.stringify(postData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return response.json();
    }
    
    async addComment(postId, commentData) {
        const response = await fetch(`${this.baseURL}/posts/${postId}/comments/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.csrfToken
            },
            body: JSON.stringify(commentData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return response.json();
    }
}

// Usage with markdown editor
document.addEventListener('DOMContentLoaded', () => {
    const apiClient = new BlogAPIClient();
    
    // Auto-save functionality
    document.addEventListener('markdown-editor:change', async (event) => {
        const { content, editorId } = event.detail;
        const postId = document.querySelector('[data-post-id]')?.dataset.postId;
        
        if (postId && content.length > 10) {
            try {
                await apiClient.updatePost(postId, { content });
                console.log('Auto-saved successfully');
            } catch (error) {
                console.error('Auto-save failed:', error);
            }
        }
    });
});
```

## Advanced Integration

### Custom Context

```python
# contexts.py
from apps.common.editor.configurations import EditorConfiguration

class DocumentEditorConfig(EditorConfiguration):
    context_name = 'document'
    
    def get_config(self):
        return {
            'toolbar': {
                'enabled': True,
                'groups': [
                    {
                        'type': 'formatting',
                        'items': ['bold', 'italic', 'code', 'strikethrough']
                    },
                    {
                        'type': 'structure',
                        'items': ['heading', 'list', 'quote', 'hr']
                    },
                    {
                        'type': 'insert',
                        'items': ['link', 'image', 'table']
                    },
                    {
                        'type': 'document',
                        'items': ['toc', 'word-count', 'export']
                    }
                ]
            },
            'features': {
                'table_of_contents': True,
                'word_count': True,
                'reading_time': True,
                'export_formats': ['pdf', 'docx', 'html']
            },
            'validation': {
                'max_length': 50000,
                'required_sections': ['Introduction', 'Conclusion']
            }
        }

# Register the context
from apps.common.editor.registry import editor_registry
editor_registry.register_context('document', DocumentEditorConfig)
```

### Custom Widget with Validation

```python
# widgets.py
from django import forms
from apps.common.editor.widgets import MarkdownEditorWidget

class DocumentEditorWidget(MarkdownEditorWidget):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('context', 'document')
        super().__init__(*args, **kwargs)
    
    def format_value(self, value):
        """Add document template if empty."""
        if not value:
            return """# Document Title

## Introduction

Brief introduction to the document.

## Main Content

Your content here.

## Conclusion

Summary and conclusion.
"""
        return super().format_value(value)

class DocumentForm(forms.Form):
    title = forms.CharField(max_length=200)
    author = forms.CharField(max_length=100)
    content = forms.CharField(
        widget=DocumentEditorWidget(),
        help_text='Write your document using the provided template'
    )
    
    def clean_content(self):
        content = self.cleaned_data['content']
        
        # Validate required sections
        required_sections = ['# ', '## Introduction', '## Conclusion']
        for section in required_sections:
            if section not in content:
                raise forms.ValidationError(
                    f'Document must include "{section.strip()}" section'
                )
        
        # Validate length
        if len(content) < 500:
            raise forms.ValidationError('Document must be at least 500 characters')
        
        return content
```

These examples show how to integrate the markdown editor into various Django patterns. For more complex scenarios, refer to the [API Reference](../api-reference.md) and [Customization Guide](../customization-guide.md).