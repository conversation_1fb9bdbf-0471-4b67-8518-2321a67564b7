{% load static %}
{# Notification Item - HTMX Fragment for Individual Notification Display #}

<div class="notification-item {% if not notification.is_read %}notification-unread{% endif %}"
     data-notification-id="{{ notification.id }}"
        {% if not notification.is_read %}
     hx-post="{% url 'notification_mark_read_toggle_htmx' notification.id %}"
     hx-target="closest .notification-item"
     hx-trigger="click"
     style="cursor: pointer;"
        {% endif %}>

  <div class="d-flex align-items-start p-3 {% if not notification.is_read %}bg-light{% endif %} border-bottom">
    {# Notification Icon #}
    <div class="notification-icon me-3 flex-shrink-0">
      <div
              class="icon-wrapper bg-{% if notification.notification_type == 'task_assigned' %}primary{% elif notification.notification_type == 'task_completed' %}success{% elif notification.notification_type == 'task_overdue' %}danger{% elif notification.notification_type == 'comment_added' %}info{% elif notification.notification_type == 'document_uploaded' %}warning{% elif notification.notification_type == 'conflict_detected' %}danger{% elif notification.notification_type == 'utility_response' %}success{% elif notification.notification_type == 'project_updated' %}primary{% else %}secondary{% endif %} text-white rounded-circle d-flex align-items-center justify-content-center"
              style="width: 40px; height: 40px;">
        {% if notification.notification_type == 'task_assigned' %}
          <i class="lucide-user-check" class="bi bi-person-check" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'task_completed' %}
          <i class="lucide-check-circle" class="bi bi-check-circle" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'task_overdue' %}
          <i class="lucide-clock" class="bi bi-clock" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'comment_added' %}
          <i class="lucide-message-circle" class="bi bi-chat" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'document_uploaded' %}
          <i class="lucide-file-plus" class="bi bi-file-plus" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'conflict_detected' %}
          <i class="lucide-alert-triangle" data-lucide="alert-triangle" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'utility_response' %}
          <i class="lucide-zap" class="bi bi-lightning" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'project_updated' %}
          <i class="lucide-folder" class="bi bi-folder" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'team_member_added' %}
          <i class="lucide-user-plus" class="bi bi-person-plus" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'deadline_approaching' %}
          <i class="lucide-calendar" class="bi bi-calendar" style="width: 20px; height: 20px;"></i>
        {% elif notification.notification_type == 'system_update' %}
          <i class="lucide-settings" class="bi bi-gear" style="width: 20px; height: 20px;"></i>
        {% else %}
          <i class="lucide-bell" class="bi bi-bell" style="width: 20px; height: 20px;"></i>
        {% endif %}
      </div>
    </div>

    {# Notification Content #}
    <div class="notification-content flex-grow-1">
      {# Header with Title and Timestamp #}
      <div class="d-flex justify-content-between align-items-start mb-1">
        <h6
                class="notification-title mb-0 {% if not notification.is_read %}fw-bold{% else %}fw-medium{% endif %} text-egis-midnight-blue">
          {{ notification.title }}
        </h6>
        <div class="d-flex align-items-center ms-3">
          {# Read Status Indicator #}
          {% if not notification.is_read %}
            <div class="unread-indicator bg-egis-green rounded-circle me-2"
                 style="width: 8px; height: 8px;"
                 title="Unread"></div>
          {% endif %}

          {# Timestamp #}
          <small class="text-muted" title="{{ notification.created_at|date:'M d, Y H:i:s' }}">
            {{ notification.created_at|timesince }} ago
          </small>
        </div>
      </div>

      {# Notification Message #}
      <p class="notification-message mb-2 text-muted">
        {{ notification.message }}
      </p>

      {# Related Object Information #}
      {% if notification.related_object %}
        <div class="related-object mb-2">
          <div class="d-flex align-items-center">
            {# Object Icon #}
            <div class="object-icon me-2 text-egis-blue">
              {% if notification.content_type.model == 'task' %}
                <i class="lucide-check-square" data-lucide="check-square" style="width: 16px; height: 16px;"></i>
              {% elif notification.content_type.model == 'project' %}
                <i class="lucide-folder" class="bi bi-folder" style="width: 16px; height: 16px;"></i>
              {% elif notification.content_type.model == 'document' %}
                <i class="lucide-file-text" class="bi bi-file-text" style="width: 16px; height: 16px;"></i>
              {% elif notification.content_type.model == 'comment' %}
                <i class="lucide-message-square" class="bi bi-chat-square" style="width: 16px; height: 16px;"></i>
              {% elif notification.content_type.model == 'utility' %}
                <i class="lucide-zap" class="bi bi-lightning" style="width: 16px; height: 16px;"></i>
              {% elif notification.content_type.model == 'conflict' %}
                <i class="lucide-alert-triangle" data-lucide="alert-triangle" style="width: 16px; height: 16px;"></i>
              {% else %}
                <i class="lucide-link" class="bi bi-link" style="width: 16px; height: 16px;"></i>
              {% endif %}
            </div>

            {# Object Link #}
            {% if notification.action_url %}
              <a href="{{ notification.action_url }}"
                 class="text-decoration-none text-egis-midnight-blue fw-medium small">
                {{ notification.related_object_name|default:"View Details" }}
              </a>
            {% else %}
              <span class="text-egis-midnight-blue fw-medium small">
                        {{ notification.related_object_name }}
                    </span>
            {% endif %}

            {# Object Type Badge #}
            <span class="badge bg-light text-muted ms-2" style="font-size: 0.7rem;">
                        {{ notification.content_type.model|title }}
                    </span>
          </div>
        </div>
      {% endif %}

      {# Project Context #}
      {% if notification.project %}
        <div class="project-context">
          <small class="text-muted">
            <i class="lucide-folder me-1" class="bi bi-folder" style="width: 12px; height: 12px;"></i>
            in <span class="fw-medium">{{ notification.project.name }}</span>
          </small>
        </div>
      {% endif %}

      {# Actor Information #}
      {% if notification.actor and notification.actor != request.user %}
        <div class="actor-info mt-2">
          <div class="d-flex align-items-center">
            {# Actor Avatar #}
            <div class="actor-avatar me-2">
              {% if notification.actor.avatar_url %}
                <img src="{{ notification.actor.avatar_url }}"
                     alt="{{ notification.actor.get_full_name }}"
                     class="rounded-circle"
                     style="width: 20px; height: 20px;" loading="lazy">
              {% else %}
                <div
                        class="bg-egis-green text-white rounded-circle d-flex align-items-center justify-content-center fw-bold"
                        style="width: 20px; height: 20px; font-size: 10px;">
                  {{ notification.actor.get_initials }}
                </div>
              {% endif %}
            </div>
            <small class="text-muted">
              by {{ notification.actor.get_full_name }}
            </small>
          </div>
        </div>
      {% endif %}
    </div>

    {# Action Buttons #}
    <div class="notification-actions ms-3 d-flex flex-column">
      {# Mark as Read/Unread Toggle #}
      <button type="button"
              class="btn btn-outline-secondary btn-sm mb-1"
              hx-post="{% url 'notification_mark_read_toggle_htmx' notification.id %}"
              hx-target="closest .notification-item"
              title="{% if notification.is_read %}Mark as unread{% else %}Mark as read{% endif %}">
        {% if notification.is_read %}
          <i class="lucide-eye-off" class="bi bi-eye-slash" style="width: 14px; height: 14px;"></i>
        {% else %}
          <i class="lucide-eye" class="bi bi-eye" style="width: 14px; height: 14px;"></i>
        {% endif %}
      </button>

      {# Action Button (if notification has action) #}
      {% if notification.action_url %}
        <a href="{{ notification.action_url }}"
           class="btn btn-egis-green btn-sm mb-1"
           title="{% if notification.action_text %}{{ notification.action_text }}{% else %}View{% endif %}">
          <i class="lucide-external-link" class="bi bi-box-arrow-up-right" style="width: 14px; height: 14px;"></i>
        </a>
      {% endif %}

      {# Dismiss Notification #}
      <button type="button"
              class="btn btn-outline-danger btn-sm"
              hx-delete="{% url 'notification_delete_htmx' notification.id %}"
              hx-target="closest .notification-item"
              hx-swap="outerHTML"
              hx-confirm="Remove this notification?"
              title="Dismiss">
        <i class="lucide-x" class="bi bi-x" style="width: 14px; height: 14px;"></i>
      </button>
    </div>
  </div>
</div>

{# Custom CSS for notification styling #}
<style>
  .notification-item {
    transition: all 0.2s ease;
  }

  .notification-unread {
    border-start: 4px solid var(--egis-green) !important;
  }

  .notification-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05) !important;
  }

  .unread-indicator {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }

  .notification-actions .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>

{# Initialize Lucide icons #}
<script>
  if (typeof lucide !== 'undefined') {
    lucide.createIcons();
  }
</script>
