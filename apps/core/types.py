"""Comprehensive type definitions for the CLEAR project.

This module provides type aliases, protocols, and type definitions used
throughout the CLEAR application to ensure type safety and better IDE support.
"""

from __future__ import annotations

from typing import (
    Any,
    Dict,
    List,
    Optional,
    Protocol,
    Tuple,
    TypeVar,
    Union,
)

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect, JsonResponse
from django.template.response import TemplateResponse

# Django type aliases
DjangoRequest = HttpRequest
DjangoResponse = Union[HttpResponse, HttpResponseRedirect, JsonResponse, TemplateResponse]
DjangoModel = models.Model
DjangoQuerySet = models.QuerySet[Any]

# Generic type variables
T = TypeVar('T')
ModelType = TypeVar('ModelType', bound=models.Model)
UserType = TypeVar('UserType', bound=AbstractUser)

# Common data structures
JSONDict = Dict[str, Any]
QueryParams = Dict[str, Union[str, int, bool, List[str]]]
FormData = Dict[str, Any]
ContextData = Dict[str, Any]
MetaData = Dict[str, Any]

# Database related types
PrimaryKey = Union[int, str]
DatabaseAlias = Optional[str]
FieldName = str
ModelFields = List[str]

# Spatial types (PostGIS)
Coordinates = Tuple[float, float]
BoundingBox = Tuple[float, float, float, float]  # (min_x, min_y, max_x, max_y)
SRID = int

# Authentication and permissions
RoleSlug = str
PermissionCode = str
OrganizationId = Union[int, str]
UserId = Union[int, str]

# API related types
APIResponse = Dict[str, Any]
APIError = Dict[str, Union[str, int, List[str]]]
PaginationData = Dict[str, Union[int, Optional[str]]]

# File and media types
FilePath = str
FileSize = int
MimeType = str
FileMetadata = Dict[str, Union[str, int]]

# Time and scheduling types
TimeRange = Tuple[Any, Any]  # Using Any for datetime objects to avoid circular imports
Duration = int  # Duration in seconds
Priority = int  # Priority level (1-5)

# Validation types
ValidationError = Dict[str, List[str]]
ValidationResult = Tuple[bool, Optional[ValidationError]]

# HTMX specific types
HTMXTarget = str
HTMXSwap = str
HTMXTrigger = str

# Analytics and reporting types
MetricValue = Union[int, float]
ChartData = Dict[str, Union[List[str], List[MetricValue]]]
ReportData = Dict[str, Any]

# Protocols for type checking
class HasOrganization(Protocol):
    """Protocol for models that have an organization relationship."""
    organization_id: int
    organization: Any  # Organization model


class HasUser(Protocol):
    """Protocol for models that have a user relationship."""
    user_id: int
    user: Any  # User model


class HasTimestamps(Protocol):
    """Protocol for models with timestamp fields."""
    created_at: Any  # datetime
    updated_at: Any  # datetime


class SoftDeletable(Protocol):
    """Protocol for models with soft delete functionality."""
    deleted_at: Optional[Any]  # Optional datetime
    is_deleted: bool


class Spatial(Protocol):
    """Protocol for models with spatial data."""
    geometry: Any  # PostGIS geometry field
    srid: int


# View related protocols
class HTMXView(Protocol):
    """Protocol for HTMX-enabled views."""
    template_name: str
    htmx_template_name: str
    
    def get_template_names(self) -> List[str]: ...


class OrganizationView(Protocol):
    """Protocol for organization-aware views."""
    def get_organization(self) -> Any: ...  # Organization model
    def check_organization_access(self) -> bool: ...


# Service layer protocols
class BaseService(Protocol):
    """Protocol for service layer classes."""
    def __init__(self, user: Any, organization: Any) -> None: ...


class CRUDService(BaseService, Protocol):
    """Protocol for CRUD service classes."""
    def create(self, data: Dict[str, Any]) -> Any: ...
    def update(self, instance: Any, data: Dict[str, Any]) -> Any: ...
    def delete(self, instance: Any) -> bool: ...
    def get_queryset(self) -> DjangoQuerySet: ...


# Form related types
FormErrors = Dict[str, List[str]]
FormField = Any  # Django form field
FormWidget = Any  # Django form widget

# Template context types
TemplateContext = Dict[str, Any]
TemplateVariable = Any

# Cache related types
CacheKey = str
CacheTimeout = int
CacheValue = Any

# Task and workflow types
TaskStatus = str
WorkflowState = str
TaskPriority = int

# Notification types
NotificationType = str
NotificationChannel = str
NotificationData = Dict[str, Any]

# Search and filtering types
SearchQuery = str
FilterParams = Dict[str, Any]
SortOrder = str
SortField = str

# Export types for easy importing
__all__ = [
    # Django types
    'DjangoRequest',
    'DjangoResponse', 
    'DjangoModel',
    'DjangoQuerySet',
    
    # Generic types
    'T',
    'ModelType',
    'UserType',
    
    # Data structures
    'JSONDict',
    'QueryParams',
    'FormData',
    'ContextData',
    'MetaData',
    
    # Database types
    'PrimaryKey',
    'DatabaseAlias',
    'FieldName',
    'ModelFields',
    
    # Spatial types
    'Coordinates',
    'BoundingBox',
    'SRID',
    
    # Auth types
    'RoleSlug',
    'PermissionCode',
    'OrganizationId',
    'UserId',
    
    # API types
    'APIResponse',
    'APIError',
    'PaginationData',
    
    # File types
    'FilePath',
    'FileSize',
    'MimeType',
    'FileMetadata',
    
    # Time types
    'TimeRange',
    'Duration',
    'Priority',
    
    # Validation types
    'ValidationError',
    'ValidationResult',
    
    # HTMX types
    'HTMXTarget',
    'HTMXSwap',
    'HTMXTrigger',
    
    # Analytics types
    'MetricValue',
    'ChartData',
    'ReportData',
    
    # Protocols
    'HasOrganization',
    'HasUser',
    'HasTimestamps',
    'SoftDeletable',
    'Spatial',
    'HTMXView',
    'OrganizationView',
    'BaseService',
    'CRUDService',
    
    # Form types
    'FormErrors',
    'FormField',
    'FormWidget',
    
    # Template types
    'TemplateContext',
    'TemplateVariable',
    
    # Cache types
    'CacheKey',
    'CacheTimeout',
    'CacheValue',
    
    # Task types
    'TaskStatus',
    'WorkflowState',
    'TaskPriority',
    
    # Notification types
    'NotificationType',
    'NotificationChannel',
    'NotificationData',
    
    # Search types
    'SearchQuery',
    'FilterParams',
    'SortOrder',
    'SortField',
]
