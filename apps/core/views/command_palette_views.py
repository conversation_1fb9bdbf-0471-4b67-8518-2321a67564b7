import json

from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.views.decorators.http import require_http_methods

from apps.authentication.models import UserProfile
from apps.core.services.command_palette_service import CommandPaletteService

User = get_user_model()


@login_required
@require_http_methods(["GET"])
def command_palette_data(request):
    service = CommandPaletteService(request.user)
    commands = service.get_available_commands(request)

    return JsonResponse(
        {
            "commands": commands,
            "categories": service.get_categories(),
            "shortcuts": service.get_keyboard_shortcuts(),
            "user_preferences": service.get_user_preferences(),
        }
    )


@login_required
@require_http_methods(["GET"])
def command_palette_search(request):
    query = request.GET.get("q", "").strip()
    category = request.GET.get("category", "")

    service = CommandPaletteService(request.user)
    results = service.search_commands(query, category, request)

    context = {
        "commands": results,
        "query": query,
        "category": category,
        "total_count": len(results),
    }

    return render(request, "components/command_palette_results.html", context)


@login_required
@require_http_methods(["POST"])
def save_command_preferences(request):
    try:
        data = json.loads(request.body)
        preferences = data.get("preferences", {})

        allowed_keys = {
            "recent_commands",
            "custom_shortcuts",
            "hidden_commands",
            "preferred_categories",
            "search_history",
            "theme_preference",
        }

        filtered_preferences = {k: v for k, v in preferences.items() if k in allowed_keys}

        profile, created = UserProfile.objects.get_or_create(user=request.user)
        profile_prefs = profile.notification_preferences or {}
        if "command_palette_preferences" not in profile_prefs:
            profile_prefs["command_palette_preferences"] = {}

        profile_prefs["command_palette_preferences"].update(filtered_preferences)
        profile.notification_preferences = profile_prefs
        profile.save()

        cache_key = f"command_palette_prefs_{request.user.id}"
        cache.set(cache_key, filtered_preferences, 3600)

        return JsonResponse({"success": True, "message": "Preferences saved successfully"})

    except (ConnectionError, TimeoutError, AttributeError, KeyError) as e:
        return JsonResponse({"success": False, "error": str(e)}, status=400)


@login_required
@require_http_methods(["POST"])
def track_command_usage(request):
    try:
        data = json.loads(request.body)
        command_id = data.get("command_id")
        execution_time = data.get("execution_time", 0)

        if not command_id:
            return JsonResponse({"error": "command_id required"}, status=400)

        service = CommandPaletteService(request.user)
        service.track_command_usage(command_id, execution_time)

        return JsonResponse({"success": True})

    except (json.JSONDecodeError, ValueError) as e:
        return JsonResponse({"error": str(e)}, status=400)


@login_required
@require_http_methods(["GET", "POST"])
def custom_commands(request):
    if request.method == "GET":
        service = CommandPaletteService(request.user)
        commands = service.get_custom_commands()
        return JsonResponse({"custom_commands": commands})

    try:
        data = json.loads(request.body)
        action = data.get("action")

        service = CommandPaletteService(request.user)

        if action == "create":
            command_data = data.get("command", {})
            result = service.create_custom_command(command_data)
            return JsonResponse(result)

        if action == "update":
            command_id = data.get("command_id")
            command_data = data.get("command", {})
            result = service.update_custom_command(command_id, command_data)
            return JsonResponse(result)

        if action == "delete":
            command_id = data.get("command_id")
            result = service.delete_custom_command(command_id)
            return JsonResponse(result)

        return JsonResponse({"error": "Invalid action"}, status=400)

    except (json.JSONDecodeError, ValueError) as e:
        return JsonResponse({"error": str(e)}, status=400)


@login_required
@require_http_methods(["GET"])
def command_analytics(request):
    days = int(request.GET.get("days", 30))
    service = CommandPaletteService(request.user)
    analytics = service.get_usage_analytics(days)
    return JsonResponse(analytics)


@login_required
@require_http_methods(["GET"])
def recent_commands(request):
    limit = int(request.GET.get("limit", 5))
    service = CommandPaletteService(request.user)
    recent = service.get_recent_commands(limit)

    context = {
        "recent_commands": recent,
        "show_shortcuts": request.GET.get("shortcuts", "true").lower() == "true",
    }

    return render(request, "components/recent_commands.html", context)


@login_required
@require_http_methods(["POST"])
def execute_command(request):
    try:
        data = json.loads(request.body)
        command_id = data.get("command_id")
        context = data.get("context", {})

        if not command_id:
            return JsonResponse({"error": "command_id required"}, status=400)

        service = CommandPaletteService(request.user)
        result = service.execute_command(command_id, context, request)

        if result.get("redirect"):
            response = HttpResponse()
            response["HX-Redirect"] = result["redirect"]
            return response

        if result.get("htmx_response"):
            return render(request, result["template"], result.get("context", {}))

        if result.get("action"):
            response = HttpResponse()
            response["HX-Trigger"] = json.dumps(
                {
                    "commandPaletteAction": {
                        "action": result["action"],
                        "data": result.get("data", {}),
                    },
                }
            )
            return response

        return JsonResponse(result)

    except (json.JSONDecodeError, ValueError) as e:
        return JsonResponse({"error": str(e)}, status=400)


@login_required
@require_http_methods(["GET"])
def command_help(request):
    help_type = request.GET.get("type", "shortcuts")

    context = {
        "help_type": help_type,
        "keyboard_shortcuts": CommandPaletteService.get_default_shortcuts(),
        "command_categories": CommandPaletteService.get_default_categories(),
    }

    return render(request, "components/command_palette_help.html", context)


@login_required
@require_http_methods(["GET"])
def command_suggestions(request):
    context_type = request.GET.get("context", "general")
    limit = int(request.GET.get("limit", 3))

    service = CommandPaletteService(request.user)
    suggestions = service.get_command_suggestions(context_type, limit, request)

    context = {"suggestions": suggestions, "context_type": context_type}

    return render(request, "components/command_suggestions.html", context)


@login_required
@require_http_methods(["POST"])
def reset_command_data(request):
    try:
        data = json.loads(request.body)
        reset_type = data.get("type", "all")

        service = CommandPaletteService(request.user)
        result = service.reset_user_data(reset_type)

        messages.success(request, "Command palette data has been reset.")
        return JsonResponse(result)

    except (json.JSONDecodeError, ValueError) as e:
        return JsonResponse({"error": str(e)}, status=400)


@login_required
@require_http_methods(["GET"])
def quick_navigation_widget(request):
    service = CommandPaletteService(request.user)
    navigation_items = service.get_quick_navigation_items(request)

    context = {
        "navigation_items": navigation_items,
        "show_descriptions": request.GET.get("descriptions", "false").lower() == "true",
    }

    return render(request, "components/quick_navigation_widget.html", context)


@login_required
@require_http_methods(["GET"])
def command_palette_widget(request):
    service = CommandPaletteService(request.user)

    context = {
        "user_preferences": service.get_user_preferences(),
        "recent_commands": service.get_recent_commands(5),
        "keyboard_shortcuts_enabled": True,
        "show_help": request.GET.get("help", "true").lower() == "true",
    }

    return render(request, "components/command_palette.html", context)
