"""
HTMX views for Import Template field mapping functionality.

Provides server-side logic for the drag-and-drop field mapping interface,
including auto-mapping, validation, and transformation configuration.
"""

import json
import logging
from typing import Any, Dict, List, Tuple

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.views import View
from django.views.generic import TemplateView

from apps.common.mixins import HTMXResponseMixin, OrganizationAccessMixin
from apps.infrastructure.models_file_import import Import
from apps.infrastructure.models_import_template import ImportTemplate
from apps.infrastructure.services.import_template_history_service import (
    ImportTemplateHistoryService,
)
from apps.infrastructure.services.validation_preview_service import (
    ValidationPreviewConfig,
    ValidationPreviewService,
)

logger = logging.getLogger(__name__)


class ImportTemplateMappingView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, TemplateView):
    """Main view for the import template field mapping interface."""

    template_name = "infrastructure/file_import/import_template_mapping.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        template_id = kwargs.get("template_id")
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=self.request.user.organization,
        )

        context.update(
            {
                "template": template,
                "source_fields": self._get_source_fields(template),
                "target_fields": self._get_target_fields(template),
                "current_mappings": template.field_mappings,
                "unmapped_required_fields": self._get_unmapped_required_fields(template),
                "sample_data_source": self._get_sample_data_source(template),
            }
        )

        return context

    def _get_source_fields(self, template: ImportTemplate) -> List[Dict[str, Any]]:
        """Get available source fields based on template configuration."""
        # This would typically come from analyzing a sample file
        # For now, we'll return a mock structure
        sample_fields = [
            {
                "name": "customer_name",
                "type": "text",
                "required": True,
                "description": "Customer full name",
                "sample_values": ["John Doe", "Jane Smith", "Bob Johnson"],
            },
            {
                "name": "email_address",
                "type": "email",
                "required": True,
                "description": "Primary email contact",
                "sample_values": ["<EMAIL>", "<EMAIL>"],
            },
            {
                "name": "phone_number",
                "type": "text",
                "required": False,
                "description": "Contact phone number",
                "sample_values": ["(*************", "************"],
            },
            {
                "name": "order_date",
                "type": "date",
                "required": True,
                "description": "Date of order placement",
                "sample_values": ["2023-07-15", "2023-07-20"],
            },
            {
                "name": "order_amount",
                "type": "number",
                "required": True,
                "description": "Total order value",
                "sample_values": ["125.50", "89.99", "234.75"],
            },
            {
                "name": "status",
                "type": "choice",
                "required": False,
                "description": "Order status",
                "sample_values": ["pending", "completed", "cancelled"],
            },
        ]
        return sample_fields

    def _get_target_fields(self, template: ImportTemplate) -> List[Dict[str, Any]]:
        """Get target schema fields for the template."""
        # This would come from the template's target schema definition
        # For now, we'll return a mock structure based on the template type
        target_fields = [
            {
                "name": "name",
                "label": "Full Name",
                "type": "text",
                "required": True,
                "description": "Customer or contact full name",
            },
            {
                "name": "email",
                "label": "Email Address",
                "type": "email",
                "required": True,
                "description": "Primary email address",
            },
            {
                "name": "phone",
                "label": "Phone Number",
                "type": "text",
                "required": False,
                "description": "Contact phone number",
            },
            {
                "name": "created_date",
                "label": "Created Date",
                "type": "date",
                "required": True,
                "description": "Record creation date",
            },
            {
                "name": "amount",
                "label": "Amount",
                "type": "decimal",
                "required": False,
                "description": "Monetary amount",
            },
            {
                "name": "status",
                "label": "Status",
                "type": "choice",
                "required": False,
                "description": "Record status",
            },
            {
                "name": "notes",
                "label": "Notes",
                "type": "text",
                "required": False,
                "description": "Additional notes or comments",
            },
        ]
        return target_fields

    def _get_unmapped_required_fields(self, template: ImportTemplate) -> List[str]:
        """Get list of required target fields that aren't mapped."""
        target_fields = self._get_target_fields(template)
        required_fields = [f["name"] for f in target_fields if f["required"]]
        mapped_fields = [mapping["target_field"] for mapping in template.field_mappings]

        return [field for field in required_fields if field not in mapped_fields]

    def _get_sample_data_source(self, template: ImportTemplate) -> str:
        """Get description of sample data source."""
        # This would typically reference an uploaded sample file
        return f"Sample {template.template_type.upper()} file"


class CreateFieldMappingView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view to create a new field mapping."""

    def post(self, request, template_id):
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        source_field = request.POST.get("source_field")
        target_field = request.POST.get("target_field")
        source_type = request.POST.get("source_type")
        target_type = request.POST.get("target_type")

        if not all([source_field, target_field, source_type, target_type]):
            messages.error(request, "Missing required mapping parameters")
            return self._render_mapping_content(request, template)

        # Remove any existing mapping for this target field
        template.field_mappings = [
            mapping for mapping in template.field_mappings if mapping["target_field"] != target_field
        ]

        # Create the new mapping
        new_mapping = {
            "source_field": source_field,
            "target_field": target_field,
            "source_type": source_type,
            "target_type": target_type,
            "transformations": self._get_default_transformations(source_type, target_type),
            "validation": self._get_default_validation(target_type),
            "default_value": None,
        }

        template.field_mappings.append(new_mapping)
        template.save()

        logger.info(
            f"Created field mapping: {source_field} -> {target_field} "
            f"for template {template.name} (user: {request.user.username})"
        )

        messages.success(request, f"Successfully mapped '{source_field}' to '{target_field}'")

        return self._render_mapping_content(request, template)

    def _get_default_transformations(self, source_type: str, target_type: str) -> List[Dict[str, Any]]:
        """Get default transformations based on field types."""
        transformations = []

        # Add type conversion if needed
        if source_type != target_type:
            if source_type == "text" and target_type in [
                "number",
                "integer",
                "float",
                "decimal",
            ]:
                transformations.append(
                    {
                        "type": "numeric_transform",
                        "params": {"type": target_type, "operation": "convert"},
                    }
                )
            elif source_type == "text" and target_type in ["date", "datetime"]:
                transformations.append(
                    {
                        "type": "date_transform",
                        "params": {"input_format": "auto", "operation": "parse"},
                    }
                )
            elif source_type in ["number", "integer", "float"] and target_type == "text":
                transformations.append(
                    {
                        "type": "string_transform",
                        "params": {"operation": "string_convert"},
                    }
                )

        # Add common cleanup transformations
        if target_type in ["text", "email", "varchar"]:
            transformations.insert(0, {"type": "string_transform", "params": {"operation": "trim"}})

        return transformations

    def _get_default_validation(self, target_type: str) -> Dict[str, Any]:
        """Get default validation rules based on target field type."""
        validation = {}

        if target_type == "email":
            validation["type"] = "email"
        elif target_type in ["number", "integer", "float", "decimal"]:
            validation["type"] = "numeric"
        elif target_type in ["date", "datetime"]:
            validation["type"] = "date"
        elif target_type == "boolean":
            validation["type"] = "boolean"

        return validation

    def _render_mapping_content(self, request, template):
        """Render the field mapping content partial."""
        context = {
            "template": template,
            "source_fields": ImportTemplateMappingView()._get_source_fields(template),
            "target_fields": ImportTemplateMappingView()._get_target_fields(template),
            "current_mappings": template.field_mappings,
            "unmapped_required_fields": ImportTemplateMappingView()._get_unmapped_required_fields(template),
        }

        return render(request, "infrastructure/file_import/partials/mapping_content.html", context)


class RemoveFieldMappingView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view to remove a field mapping."""

    def post(self, request, template_id):
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        source_field = request.POST.get("source_field")

        if not source_field:
            messages.error(request, "Missing source field parameter")
            return self._render_mapping_content(request, template)

        # Remove the mapping
        original_count = len(template.field_mappings)
        template.field_mappings = [
            mapping for mapping in template.field_mappings if mapping["source_field"] != source_field
        ]

        if len(template.field_mappings) < original_count:
            template.save()
            logger.info(
                f"Removed field mapping for '{source_field}' "
                f"from template {template.name} (user: {request.user.username})"
            )
            messages.success(request, f"Removed mapping for '{source_field}'")
        else:
            messages.warning(request, f"No mapping found for '{source_field}'")

        return self._render_mapping_content(request, template)

    def _render_mapping_content(self, request, template):
        """Render the field mapping content partial."""
        context = {
            "template": template,
            "source_fields": ImportTemplateMappingView()._get_source_fields(template),
            "target_fields": ImportTemplateMappingView()._get_target_fields(template),
            "current_mappings": template.field_mappings,
            "unmapped_required_fields": ImportTemplateMappingView()._get_unmapped_required_fields(template),
        }

        return render(request, "infrastructure/file_import/partials/mapping_content.html", context)


class AutoMapFieldsView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view to automatically map fields based on names and types."""

    def post(self, request, template_id):
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        source_fields = ImportTemplateMappingView()._get_source_fields(template)
        target_fields = ImportTemplateMappingView()._get_target_fields(template)

        auto_mappings = self._generate_auto_mappings(source_fields, target_fields)

        # Clear existing mappings and add auto-generated ones
        template.field_mappings = []

        for source_field, target_field, confidence in auto_mappings:
            new_mapping = {
                "source_field": source_field["name"],
                "target_field": target_field["name"],
                "source_type": source_field["type"],
                "target_type": target_field["type"],
                "transformations": self._get_default_transformations(source_field["type"], target_field["type"]),
                "validation": self._get_default_validation(target_field["type"]),
                "default_value": None,
                "auto_mapped": True,
                "confidence": confidence,
            }
            template.field_mappings.append(new_mapping)

        template.save()

        logger.info(
            f"Auto-mapped {len(auto_mappings)} fields for template {template.name} (user: {request.user.username})"
        )

        messages.success(request, f"Auto-mapped {len(auto_mappings)} fields with high confidence")

        return self._render_mapping_content(request, template)

    def _generate_auto_mappings(self, source_fields: List[Dict], target_fields: List[Dict]) -> List[Tuple]:
        """Generate automatic field mappings based on name similarity and type compatibility."""
        mappings = []
        used_targets = set()

        for source_field in source_fields:
            best_match = None
            best_confidence = 0.0

            for target_field in target_fields:
                if target_field["name"] in used_targets:
                    continue

                confidence = self._calculate_field_match_confidence(source_field, target_field)

                if confidence > best_confidence and confidence >= 0.7:  # Minimum confidence threshold
                    best_match = target_field
                    best_confidence = confidence

            if best_match:
                mappings.append((source_field, best_match, best_confidence))
                used_targets.add(best_match["name"])

        return mappings

    def _calculate_field_match_confidence(self, source_field: Dict, target_field: Dict) -> float:
        """Calculate confidence score for field matching (0.0 to 1.0)."""
        confidence = 0.0

        # Name similarity (50% weight)
        name_similarity = self._calculate_name_similarity(source_field["name"], target_field["name"])
        confidence += name_similarity * 0.5

        # Type compatibility (30% weight)
        type_compatibility = self._calculate_type_compatibility(source_field["type"], target_field["type"])
        confidence += type_compatibility * 0.3

        # Required field matching (20% weight)
        if source_field.get("required") and target_field.get("required"):
            confidence += 0.2
        elif not source_field.get("required") and not target_field.get("required"):
            confidence += 0.1

        return min(confidence, 1.0)

    def _calculate_name_similarity(self, source_name: str, target_name: str) -> float:
        """Calculate name similarity score."""
        # Simple implementation - could be enhanced with more sophisticated algorithms
        source_clean = source_name.lower().replace("_", "").replace("-", "")
        target_clean = target_name.lower().replace("_", "").replace("-", "")

        # Exact match
        if source_clean == target_clean:
            return 1.0

        # Contains relationship
        if source_clean in target_clean or target_clean in source_clean:
            return 0.8

        # Common prefixes/suffixes
        common_mappings = {
            "email": ["email", "mail", "address"],
            "phone": ["phone", "tel", "number"],
            "name": ["name", "title", "label"],
            "date": ["date", "time", "created", "updated"],
            "amount": ["amount", "price", "cost", "value"],
            "status": ["status", "state", "condition"],
        }

        for _key, aliases in common_mappings.items():
            if any(alias in source_clean for alias in aliases) and any(alias in target_clean for alias in aliases):
                return 0.7

        return 0.0

    def _calculate_type_compatibility(self, source_type: str, target_type: str) -> float:
        """Calculate type compatibility score."""
        if source_type == target_type:
            return 1.0

        # Compatible types
        compatible_groups = [
            ["text", "varchar", "char", "choice"],
            ["number", "integer", "float", "decimal"],
            ["date", "datetime"],
            ["email", "text", "varchar"],
            ["url", "text", "varchar"],
        ]

        for group in compatible_groups:
            if source_type in group and target_type in group:
                return 0.8

        # Convertible types
        if source_type == "text" and target_type in ["number", "date", "boolean"]:
            return 0.5

        return 0.0

    def _get_default_transformations(self, source_type: str, target_type: str) -> List[Dict[str, Any]]:
        """Get default transformations based on field types."""
        return CreateFieldMappingView()._get_default_transformations(source_type, target_type)

    def _get_default_validation(self, target_type: str) -> Dict[str, Any]:
        """Get default validation rules based on target field type."""
        return CreateFieldMappingView()._get_default_validation(target_type)

    def _render_mapping_content(self, request, template):
        """Render the field mapping content partial."""
        return CreateFieldMappingView()._render_mapping_content(request, template)


class ClearFieldMappingsView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view to clear all field mappings."""

    def post(self, request, template_id):
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        mapping_count = len(template.field_mappings)
        template.field_mappings = []
        template.save()

        logger.info(
            f"Cleared {mapping_count} field mappings from template {template.name} (user: {request.user.username})"
        )

        messages.success(request, f"Cleared {mapping_count} field mappings")

        return CreateFieldMappingView()._render_mapping_content(request, template)


class ValidateMappingView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view to validate current field mappings."""

    def post(self, request, template_id):
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        validation_results = self._validate_template_mappings(template)

        context = {"template": template, "validation_results": validation_results}

        return render(
            request,
            "infrastructure/file_import/partials/mapping_validation_results.html",
            context,
        )

    def _validate_template_mappings(self, template: ImportTemplate) -> Dict[str, Any]:
        """Validate the current field mappings."""
        results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "summary": {
                "total_mappings": len(template.field_mappings),
                "required_mappings": 0,
                "optional_mappings": 0,
                "missing_required": [],
            },
        }

        target_fields = ImportTemplateMappingView()._get_target_fields(template)
        required_targets = [f["name"] for f in target_fields if f["required"]]
        mapped_targets = [m["target_field"] for m in template.field_mappings]

        # Check for missing required mappings
        missing_required = [field for field in required_targets if field not in mapped_targets]
        if missing_required:
            results["is_valid"] = False
            results["errors"].append(f"Missing required field mappings: {', '.join(missing_required)}")
            results["summary"]["missing_required"] = missing_required

        # Validate individual mappings
        for mapping in template.field_mappings:
            source_field = mapping["source_field"]
            target_field = mapping["target_field"]
            source_type = mapping["source_type"]
            target_type = mapping["target_type"]

            # Check type compatibility
            if not self._is_type_compatible(source_type, target_type):
                if not self._is_type_convertible(source_type, target_type):
                    results["warnings"].append(
                        f"Potential type mismatch: {source_field} ({source_type}) -> {target_field} ({target_type})"
                    )

            # Count mapping types
            target_info = next((f for f in target_fields if f["name"] == target_field), {})
            if target_info.get("required"):
                results["summary"]["required_mappings"] += 1
            else:
                results["summary"]["optional_mappings"] += 1

        return results

    def _is_type_compatible(self, source_type: str, target_type: str) -> bool:
        """Check if types are directly compatible."""
        return AutoMapFieldsView()._calculate_type_compatibility(source_type, target_type) >= 0.8

    def _is_type_convertible(self, source_type: str, target_type: str) -> bool:
        """Check if types are convertible with transformations."""
        return AutoMapFieldsView()._calculate_type_compatibility(source_type, target_type) >= 0.5


class MappingSuggestionsView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view to show mapping suggestions."""

    def get(self, request, template_id):
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        source_fields = ImportTemplateMappingView()._get_source_fields(template)
        target_fields = ImportTemplateMappingView()._get_target_fields(template)

        suggestions = self._generate_mapping_suggestions(source_fields, target_fields, template)

        context = {"template": template, "suggestions": suggestions}

        return render(
            request,
            "infrastructure/file_import/partials/mapping_suggestions.html",
            context,
        )

    def _generate_mapping_suggestions(
        self,
        source_fields: List[Dict],
        target_fields: List[Dict],
        template: ImportTemplate,
    ) -> List[Dict]:
        """Generate mapping suggestions with explanations."""
        suggestions = []
        mapped_targets = {m["target_field"] for m in template.field_mappings}

        for source_field in source_fields:
            for target_field in target_fields:
                if target_field["name"] in mapped_targets:
                    continue

                confidence = AutoMapFieldsView()._calculate_field_match_confidence({}, source_field, target_field)

                if confidence >= 0.6:  # Lower threshold for suggestions
                    reason = self._get_suggestion_reason(source_field, target_field, confidence)
                    suggestions.append(
                        {
                            "source_field": source_field,
                            "target_field": target_field,
                            "confidence": confidence,
                            "reason": reason,
                            "suggested_transformations": AutoMapFieldsView()._get_default_transformations(
                                {}, source_field["type"], target_field["type"]
                            ),
                        }
                    )

        # Sort by confidence descending
        suggestions.sort(key=lambda x: x["confidence"], reverse=True)

        return suggestions[:10]  # Limit to top 10 suggestions

    def _get_suggestion_reason(self, source_field: Dict, target_field: Dict, confidence: float) -> str:
        """Generate human-readable reason for the mapping suggestion."""
        reasons = []

        if source_field["name"].lower() == target_field["name"].lower():
            reasons.append("Exact name match")
        elif (
            source_field["name"].lower() in target_field["name"].lower()
            or target_field["name"].lower() in source_field["name"].lower()
        ):
            reasons.append("Similar field names")

        if source_field["type"] == target_field["type"]:
            reasons.append("Matching field types")
        elif AutoMapFieldsView()._calculate_type_compatibility({}, source_field["type"], target_field["type"]) >= 0.5:
            reasons.append("Compatible field types")

        if source_field.get("required") and target_field.get("required"):
            reasons.append("Both fields are required")

        return "; ".join(reasons) if reasons else f"Confidence: {confidence:.0%}"


class ValidationPreviewView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view for validation preview functionality."""

    def post(self, request, template_id):
        """Generate validation preview for sample data."""
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        # Get sample data from request
        sample_data = self._extract_sample_data(request)

        if not sample_data:
            context = {
                "template": template,
                "error_message": "No sample data provided for validation preview",
            }
            return render(
                request,
                "infrastructure/file_import/partials/validation_preview_error.html",
                context,
            )

        try:
            # Generate validation preview
            preview_service = ValidationPreviewService(template)
            preview_limit = int(request.POST.get("preview_limit", 50))

            preview_data = preview_service.get_validation_preview_for_template(sample_data, limit=preview_limit)

            # Get detailed row-by-row validation for display
            config = ValidationPreviewConfig(
                max_preview_rows=min(preview_limit, 10),  # Limit UI display to 10 rows
                include_transformed_data=True,
                include_field_statistics=True,
                show_warnings=True,
            )

            summary = preview_service.generate_validation_preview(sample_data, config)

            # Generate sample validation results for display
            row_results = []
            for i, data_row in enumerate(sample_data[: config.max_preview_rows]):
                try:
                    transformed_data = template.apply_field_mappings(data_row)
                    validation_result = template.validate_data(transformed_data)

                    row_results.append(
                        {
                            "row_index": i,
                            "source_data": data_row,
                            "transformed_data": transformed_data,
                            "validation_result": validation_result,
                            "is_valid": validation_result.get("is_valid", True),
                            "error_count": len(validation_result.get("errors", [])),
                            "warning_count": len(validation_result.get("warnings", [])),
                        }
                    )
                except Exception as e:
                    row_results.append(
                        {
                            "row_index": i,
                            "source_data": data_row,
                            "transformed_data": {},
                            "validation_result": {
                                "is_valid": False,
                                "errors": [f"Processing error: {str(e)}"],
                                "warnings": [],
                                "field_results": {},
                            },
                            "is_valid": False,
                            "error_count": 1,
                            "warning_count": 0,
                        }
                    )

            context = {
                "template": template,
                "preview_data": preview_data,
                "summary": summary,
                "row_results": row_results,
                "sample_data_count": len(sample_data),
                "preview_count": config.max_preview_rows,
            }

            return render(
                request,
                "infrastructure/file_import/partials/validation_preview_results.html",
                context,
            )

        except Exception as e:
            logger.error(f"Error generating validation preview: {e}")
            context = {
                "template": template,
                "error_message": f"Error generating validation preview: {str(e)}",
            }
            return render(
                request,
                "infrastructure/file_import/partials/validation_preview_error.html",
                context,
            )

    def _extract_sample_data(self, request) -> List[Dict[str, Any]]:
        """Extract sample data from request."""
        sample_data_json = request.POST.get("sample_data")

        if sample_data_json:
            try:
                return json.loads(sample_data_json)
            except json.JSONDecodeError:
                logger.error("Invalid JSON in sample_data parameter")
                return []

        # Alternative: extract from form fields
        sample_data = []
        row_count = int(request.POST.get("row_count", 0))

        for i in range(row_count):
            row_data = {}
            for key, value in request.POST.items():
                if key.startswith(f"row_{i}_"):
                    field_name = key[len(f"row_{i}_") :]
                    row_data[field_name] = value

            if row_data:
                sample_data.append(row_data)

        return sample_data


class ValidationPreviewStatisticsView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view for detailed validation statistics."""

    def get(self, request, template_id):
        """Get detailed validation statistics."""
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        # Get cached validation results or generate new ones
        sample_data = self._get_cached_sample_data(request, template)

        if not sample_data:
            context = {
                "template": template,
                "error_message": "No validation data available. Please run validation preview first.",
            }
            return render(
                request,
                "infrastructure/file_import/partials/validation_statistics_error.html",
                context,
            )

        try:
            preview_service = ValidationPreviewService(template)
            config = ValidationPreviewConfig(
                max_preview_rows=100,  # Analyze more rows for statistics
                include_field_statistics=True,
                show_warnings=True,
            )

            summary = preview_service.generate_validation_preview(sample_data, config)

            # Generate additional statistics
            field_analysis = self._analyze_field_patterns(summary.field_statistics)
            error_analysis = self._analyze_error_patterns(summary.common_errors)
            quality_analysis = self._analyze_quality_trends(sample_data, preview_service)

            context = {
                "template": template,
                "summary": summary,
                "field_analysis": field_analysis,
                "error_analysis": error_analysis,
                "quality_analysis": quality_analysis,
            }

            return render(
                request,
                "infrastructure/file_import/partials/validation_statistics_detailed.html",
                context,
            )

        except Exception as e:
            logger.error(f"Error generating validation statistics: {e}")
            context = {
                "template": template,
                "error_message": f"Error generating statistics: {str(e)}",
            }
            return render(
                request,
                "infrastructure/file_import/partials/validation_statistics_error.html",
                context,
            )

    def _get_cached_sample_data(self, request, template) -> List[Dict[str, Any]]:
        """Get sample data from session cache or database."""
        # In a real implementation, this would retrieve cached sample data
        # For now, return empty list to trigger error message
        return []

    def _analyze_field_patterns(self, field_statistics: Dict[str, Dict]) -> Dict[str, Any]:
        """Analyze patterns in field validation results."""
        analysis = {
            "most_problematic_fields": [],
            "highest_completion_fields": [],
            "lowest_completion_fields": [],
            "field_quality_scores": {},
        }

        for field_name, stats in field_statistics.items():
            completion_rate = stats["populated_records"] / max(stats["total_records"], 1)
            error_rate = stats["error_count"] / max(stats["total_records"], 1)
            quality_score = (completion_rate * 70) + ((1 - error_rate) * 30)

            analysis["field_quality_scores"][field_name] = {
                "completion_rate": completion_rate,
                "error_rate": error_rate,
                "quality_score": quality_score,
                "total_records": stats["total_records"],
                "error_count": stats["error_count"],
            }

        # Sort fields by various metrics
        sorted_by_quality = sorted(
            analysis["field_quality_scores"].items(),
            key=lambda x: x[1]["quality_score"],
        )

        analysis["most_problematic_fields"] = sorted_by_quality[:5]
        analysis["highest_completion_fields"] = sorted(
            field_statistics.items(),
            key=lambda x: x[1]["populated_records"] / max(x[1]["total_records"], 1),
            reverse=True,
        )[:5]

        analysis["lowest_completion_fields"] = sorted(
            field_statistics.items(),
            key=lambda x: x[1]["populated_records"] / max(x[1]["total_records"], 1),
        )[:5]

        return analysis

    def _analyze_error_patterns(self, common_errors: List[Dict]) -> Dict[str, Any]:
        """Analyze patterns in validation errors."""
        analysis = {
            "total_error_types": len(common_errors),
            "most_frequent_errors": common_errors[:5],
            "field_error_distribution": {},
            "error_type_distribution": {},
        }

        # Analyze error distribution by field and type
        for error in common_errors:
            field = error.get("field", "unknown")
            error_type = error.get("type", "unknown")
            count = error.get("count", 0)

            if field not in analysis["field_error_distribution"]:
                analysis["field_error_distribution"][field] = 0
            analysis["field_error_distribution"][field] += count

            if error_type not in analysis["error_type_distribution"]:
                analysis["error_type_distribution"][error_type] = 0
            analysis["error_type_distribution"][error_type] += count

        return analysis

    def _analyze_quality_trends(
        self, sample_data: List[Dict], preview_service: ValidationPreviewService
    ) -> Dict[str, Any]:
        """Analyze quality trends across the dataset."""
        analysis = {
            "quality_distribution": {
                "excellent": 0,  # 90-100%
                "good": 0,  # 80-89%
                "fair": 0,  # 70-79%
                "poor": 0,  # <70%
            },
            "recommendations": [],
            "overall_assessment": "",
        }

        # This is a simplified analysis - in practice you'd analyze actual quality scores
        total_rows = len(sample_data)

        # Generate mock quality distribution for demonstration
        analysis["quality_distribution"]["excellent"] = int(total_rows * 0.6)
        analysis["quality_distribution"]["good"] = int(total_rows * 0.2)
        analysis["quality_distribution"]["fair"] = int(total_rows * 0.15)
        analysis["quality_distribution"]["poor"] = total_rows - sum(analysis["quality_distribution"].values())

        # Generate recommendations based on quality distribution
        if analysis["quality_distribution"]["poor"] > total_rows * 0.2:
            analysis["recommendations"].append("High percentage of poor quality records. Consider data cleanup.")

        if analysis["quality_distribution"]["excellent"] > total_rows * 0.8:
            analysis["overall_assessment"] = "Excellent data quality - ready for import"
        elif (
            analysis["quality_distribution"]["good"] + analysis["quality_distribution"]["excellent"] > total_rows * 0.7
        ):
            analysis["overall_assessment"] = "Good data quality - minor issues to address"
        else:
            analysis["overall_assessment"] = "Data quality needs improvement before import"

        return analysis


class ValidationPreviewExportView(LoginRequiredMixin, OrganizationAccessMixin, View):
    """Export validation preview results to various formats."""

    def get(self, request, template_id):
        """Export validation results."""
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        export_format = request.GET.get("format", "json")

        # Get validation data (would be cached in real implementation)
        sample_data = []  # Placeholder

        if not sample_data:
            return JsonResponse({"error": "No validation data available"}, status=400)

        try:
            preview_service = ValidationPreviewService(template)
            summary = preview_service.generate_validation_preview(sample_data)

            if export_format == "json":
                return self._export_json(summary, template)
            elif export_format == "csv":
                return self._export_csv(summary, template)
            else:
                return JsonResponse({"error": "Unsupported export format"}, status=400)

        except Exception as e:
            logger.error(f"Error exporting validation results: {e}")
            return JsonResponse({"error": str(e)}, status=500)

    def _export_json(self, summary, template):
        """Export as JSON."""
        from django.http import JsonResponse

        export_data = {
            "template": {
                "id": str(template.template_id),
                "name": template.name,
                "version": template.version,
            },
            "validation_summary": {
                "total_rows": summary.total_rows,
                "valid_rows": summary.valid_rows,
                "invalid_rows": summary.invalid_rows,
                "error_count": summary.error_count,
                "warning_count": summary.warning_count,
                "quality_score": summary.overall_quality_score,
            },
            "field_statistics": summary.field_statistics,
            "common_errors": summary.common_errors,
            "export_timestamp": timezone.now().isoformat(),
        }

        response = JsonResponse(export_data, json_dumps_params={"indent": 2})
        response["Content-Disposition"] = f'attachment; filename="validation_results_{template.name}.json"'
        return response

    def _export_csv(self, summary, template):
        """Export as CSV."""
        import csv

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = f'attachment; filename="validation_results_{template.name}.csv"'

        writer = csv.writer(response)

        # Write summary information
        writer.writerow(["Validation Summary"])
        writer.writerow(["Template", template.name])
        writer.writerow(["Total Rows", summary.total_rows])
        writer.writerow(["Valid Rows", summary.valid_rows])
        writer.writerow(["Invalid Rows", summary.invalid_rows])
        writer.writerow(["Overall Quality Score", f"{summary.overall_quality_score:.1f}%"])
        writer.writerow([])

        # Write field statistics
        writer.writerow(["Field Statistics"])
        writer.writerow(
            [
                "Field Name",
                "Total Records",
                "Populated Records",
                "Valid Records",
                "Error Count",
            ]
        )

        for field_name, stats in summary.field_statistics.items():
            writer.writerow(
                [
                    field_name,
                    stats["total_records"],
                    stats["populated_records"],
                    stats["valid_records"],
                    stats["error_count"],
                ]
            )

        writer.writerow([])

        # Write common errors
        writer.writerow(["Common Errors"])
        writer.writerow(["Field", "Error Type", "Count", "Message"])

        for error in summary.common_errors:
            writer.writerow(
                [
                    error.get("field", ""),
                    error.get("type", ""),
                    error.get("count", 0),
                    error.get("message", ""),
                ]
            )

        return response


class TemplateImportHistoryView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view for template import history with advanced filtering."""

    def get(self, request, template_id):
        """Get import history for a specific template."""
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        # Initialize history service
        history_service = ImportTemplateHistoryService()

        # Get filter parameters
        status_filter = request.GET.get("status")
        days_filter = int(request.GET.get("days", 30))
        limit = int(request.GET.get("limit", 20))
        offset = int(request.GET.get("offset", 0))

        # Calculate date range
        from datetime import timedelta

        from django.utils import timezone

        end_date = timezone.now()
        start_date = end_date - timedelta(days=days_filter)

        try:
            # Get template imports
            imports = history_service.get_template_imports(
                template_id=str(template_id),
                user=request.user,
                organization=request.user.organization,
                status_filter=status_filter,
                date_range=(start_date, end_date),
                limit=limit,
                offset=offset,
            )

            # Get template analytics
            analytics = history_service.get_template_usage_analytics(
                template_id=str(template_id),
                user=request.user,
                organization=request.user.organization,
                date_range=(start_date, end_date),
            )

            # Calculate pagination info
            total_imports = Import.objects.filter(
                import_template=template,
                created_by=request.user,
                created_at__gte=start_date,
            ).count()

            has_more = (offset + limit) < total_imports

            context = {
                "template": template,
                "imports": imports,
                "analytics": analytics,
                "total_imports": total_imports,
                "has_more": has_more,
                "next_offset": offset + limit,
                "status_filter": status_filter,
                "days_filter": days_filter,
                "limit": limit,
            }

            return render(
                request,
                "infrastructure/file_import/partials/template_import_history.html",
                context,
            )

        except Exception as e:
            logger.error(f"Error fetching template import history: {e}")
            context = {
                "template": template,
                "error_message": f"Error loading import history: {str(e)}",
            }
            return render(
                request,
                "infrastructure/file_import/partials/template_import_history_error.html",
                context,
            )

    def post(self, request, template_id):
        """Handle template import history actions (retry, delete, etc.)."""
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        action = request.POST.get("action")
        import_id = request.POST.get("import_id")

        if not action or not import_id:
            return JsonResponse({"error": "Missing action or import_id"}, status=400)

        try:
            import_record = get_object_or_404(
                Import,
                import_id=import_id,
                import_template=template,
                created_by=request.user,
            )

            if action == "retry":
                # Create a retry import
                history_service = ImportTemplateHistoryService()
                new_import = history_service.create_import_from_template(
                    template=template,
                    upload=import_record.upload,
                    user=request.user,
                    name=f"Retry: {import_record.name}",
                    description=f"Retry of import {import_record.import_id}",
                )
                new_import.parent_import = import_record
                new_import.import_type = "retry"
                new_import.save()

                messages.success(request, f"Retry import created: {new_import.name}")

            elif action == "delete":
                import_name = import_record.name
                import_record.delete()
                messages.success(request, f"Deleted import: {import_name}")

            else:
                return JsonResponse({"error": f"Unknown action: {action}"}, status=400)

            # Return updated history
            return self.get(request, template_id)

        except Exception as e:
            logger.error(f"Error handling import history action: {e}")
            return JsonResponse({"error": str(e)}, status=500)


class TemplateImportStatisticsView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view for detailed template import statistics."""

    def get(self, request, template_id):
        """Get detailed statistics for template imports."""
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        # Initialize history service
        history_service = ImportTemplateHistoryService()

        # Get date range filter
        days_filter = int(request.GET.get("days", 30))

        from datetime import timedelta

        from django.utils import timezone

        end_date = timezone.now()
        start_date = end_date - timedelta(days=days_filter)

        try:
            # Get comprehensive statistics
            statistics = history_service.get_template_import_statistics(
                template_id=str(template_id),
                user=request.user,
                organization=request.user.organization,
                date_range=(start_date, end_date),
            )

            # Get template analytics
            analytics = history_service.get_template_usage_analytics(
                template_id=str(template_id),
                user=request.user,
                organization=request.user.organization,
                date_range=(start_date, end_date),
            )

            context = {
                "template": template,
                "statistics": statistics,
                "analytics": analytics,
                "days_filter": days_filter,
            }

            return render(
                request,
                "infrastructure/file_import/partials/template_import_statistics.html",
                context,
            )

        except Exception as e:
            logger.error(f"Error generating template statistics: {e}")
            context = {
                "template": template,
                "error_message": f"Error generating statistics: {str(e)}",
            }
            return render(
                request,
                "infrastructure/file_import/partials/template_import_statistics_error.html",
                context,
            )


class TemplateImportDetailView(LoginRequiredMixin, OrganizationAccessMixin, HTMXResponseMixin, View):
    """HTMX view for detailed template import information."""

    def get(self, request, template_id, import_id):
        """Get detailed information about a specific template import."""
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        import_record = get_object_or_404(
            Import,
            import_id=import_id,
            import_template=template,
            created_by=request.user,
        )

        # Get related errors
        import_errors = import_record.upload.import_errors_detailed.all().order_by("-created_at")[:20]

        # Parse validation preview summary
        validation_summary = import_record.validation_preview_summary
        if isinstance(validation_summary, str):
            import json

            try:
                validation_summary = json.loads(validation_summary)
            except json.JSONDecodeError:
                validation_summary = {}

        # Parse template configuration snapshot
        template_config = import_record.template_configuration_snapshot
        if isinstance(template_config, str):
            try:
                template_config = json.loads(template_config)
            except json.JSONDecodeError:
                template_config = {}

        context = {
            "template": template,
            "import_record": import_record,
            "import_errors": import_errors,
            "validation_summary": validation_summary,
            "template_config": template_config,
        }

        return render(
            request,
            "infrastructure/file_import/partials/template_import_detail.html",
            context,
        )


class TemplateImportExportView(LoginRequiredMixin, OrganizationAccessMixin, View):
    """Export template import history in various formats."""

    def get(self, request, template_id):
        """Export template import history."""
        template = get_object_or_404(
            ImportTemplate,
            template_id=template_id,
            organization=request.user.organization,
        )

        export_format = request.GET.get("format", "json")

        # Initialize history service
        history_service = ImportTemplateHistoryService()

        try:
            # Get export data
            export_data = history_service.export_template_history(
                template_id=str(template_id),
                format=export_format,
                user=request.user,
                organization=request.user.organization,
            )

            if export_format == "json":
                return self._export_json(export_data, template)
            elif export_format == "csv":
                return self._export_csv(export_data, template)
            else:
                return JsonResponse({"error": "Unsupported export format"}, status=400)

        except Exception as e:
            logger.error(f"Error exporting template history: {e}")
            return JsonResponse({"error": str(e)}, status=500)

    def _export_json(self, export_data, template):
        """Export as JSON."""
        from django.http import JsonResponse

        response = JsonResponse(export_data, json_dumps_params={"indent": 2})
        response["Content-Disposition"] = f'attachment; filename="template_history_{template.name}.json"'
        return response

    def _export_csv(self, export_data, template):
        """Export as CSV."""
        import csv

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = f'attachment; filename="template_history_{template.name}.csv"'

        writer = csv.writer(response)

        # Write template information
        writer.writerow(["Template Import History"])
        writer.writerow(["Template Name", template.name])
        writer.writerow(["Template ID", export_data["template_info"]["template_id"]])
        writer.writerow(["Export Date", export_data["export_timestamp"]])
        writer.writerow([])

        # Write analytics summary
        writer.writerow(["Analytics Summary"])
        analytics = export_data["analytics"]
        writer.writerow(["Usage Count", analytics["usage_count"]])
        writer.writerow(["Success Rate", f"{analytics['success_rate']:.1f}%"])
        writer.writerow(
            [
                "Average Processing Time",
                f"{analytics['average_processing_time']:.2f} seconds",
            ]
        )
        writer.writerow(["Average Quality Score", f"{analytics['average_quality_score']:.1f}%"])
        writer.writerow(["Recommendation Score", f"{analytics['recommendation_score']:.1f}"])
        writer.writerow([])

        # Write import history
        writer.writerow(["Import History"])
        writer.writerow(
            [
                "Import ID",
                "Name",
                "Status",
                "Created At",
                "Processing Time",
                "Total Features",
                "Imported Features",
                "Transformed Features",
                "Error Features",
                "Validation Warnings",
                "Created By",
            ]
        )

        for import_data in export_data["imports"]:
            writer.writerow(
                [
                    import_data["import_id"],
                    import_data["name"],
                    import_data["status"],
                    import_data["created_at"],
                    f"{import_data['processing_time'] or 0:.2f}",
                    import_data["total_features"],
                    import_data["imported_features"],
                    import_data["transformed_features"],
                    import_data["error_features"],
                    import_data["validation_warnings"],
                    import_data["created_by"] or "Unknown",
                ]
            )

        return response
