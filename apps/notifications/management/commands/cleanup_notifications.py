"""
Management command to cleanup old notifications according to retention policy.

This command should be run daily via cron to maintain database performance
and comply with data retention policies.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone

from apps.notifications.services.notification_performance import (
    NotificationPerformanceService,
)


class Command(BaseCommand):
    help = "Cleanup old notifications according to retention policy"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be deleted without actually deleting notifications",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Actually delete notifications (required for non-dry-run)",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Enable verbose output",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]
        force = options["force"]
        verbose = options["verbose"]

        if not dry_run and not force:
            self.stdout.write(
                self.style.ERROR("Must specify either --dry-run or --force to prevent accidental deletions")
            )
            return

        if verbose:
            self.stdout.write(f"Starting notification cleanup at {timezone.now()}")

        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No notifications will be deleted"))

        try:
            service = NotificationPerformanceService()
            results = service.apply_retention_policy(dry_run=dry_run)

            # Display results
            total_to_delete = sum(results.values())

            if total_to_delete == 0:
                self.stdout.write(self.style.SUCCESS("No notifications need cleanup at this time"))
                return

            self.stdout.write(f"\nNotification cleanup {'would delete' if dry_run else 'deleted'}:")
            self.stdout.write(f"  - Read notifications (old): {results['read_deleted']}")
            self.stdout.write(f"  - Expired notifications: {results['expired_deleted']}")
            self.stdout.write(f"  - Very old notifications: {results['old_deleted']}")
            self.stdout.write(f"  - Failed notifications (old): {results['failed_deleted']}")
            self.stdout.write(f"  - Total: {total_to_delete}")

            if not dry_run:
                self.stdout.write(self.style.SUCCESS(f"\nSuccessfully cleaned up {total_to_delete} notifications"))
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"\nWould delete {total_to_delete} notifications. Use --force to actually delete."
                    )
                )

            if verbose:
                self.stdout.write(f"Completed cleanup at {timezone.now()}")

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error during notification cleanup: {str(e)}"))
            raise
