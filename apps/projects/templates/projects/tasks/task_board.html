{% extends "base.html" %}

{% load i18n %}
{% load static %}

{% block title %}
  {% trans "Task Board" %} - {{ current_project.name|default:"All Projects" }}
{% endblock %}

{% block extra_css %}
  <link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css"
        rel="stylesheet">
  <link href="{% static 'css/features/task-drag-drop.css' %}"
        rel="stylesheet">
  <style>
.kanban-board-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.view-toggle {
    background: white;
    border-radius: 8px;
    padding: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.view-toggle .btn {
    border: none;
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.view-toggle .btn.active {
    background: #007bff;
    color: white;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

.project-selector {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 1rem;
}
  </style>
{% endblock %}

{% block content %}
  <div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
      <div class="col-12">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'projects:project-list' %}">{% trans "Projects" %}</a>
            </li>

            {% if current_project %}
              <li class="breadcrumb-item">
                <a href="{% url 'projects:project-detail' current_project.pk %}">{{ current_project.name }}</a>
              </li>
            {% endif %}

            <li class="breadcrumb-item active" aria-current="page">{% trans "Task Board" %}</li>
          </ol>
        </nav>
      </div>
    </div>
    <!-- Page Header -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h1 class="h2 mb-2">
              <i class="bi bi-kanban me-2"></i>
              {% trans "Task Board" %}
            </h1>
            <p class="text-muted mb-0">{% trans "Drag and drop tasks to update status and priority" %}</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-primary"
                    hx-get="{% url 'projects:task_create' %}"
                    hx-target="#task-modal .modal-content"
                    hx-trigger="click"
                    data-bs-toggle="modal"
                    data-bs-target="#task-modal">
              <i class="bi bi-plus-circle me-2"></i>
              {% trans "Add Task" %}
            </button>
            <div class="view-toggle">
              <button class="btn active"
                      id="kanban-view-btn"
                      onclick="toggleView('kanban')">
                <i class="bi bi-kanban"></i> {% trans "Kanban" %}
              </button>
              <button class="btn" id="list-view-btn" onclick="toggleView('list')">
                <i class="bi bi-list-ul"></i> {% trans "List" %}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Project Selector -->

    {% if not current_project %}
      <div class="row mb-4">
        <div class="col-12">
          <div class="project-selector">
            <label for="project-filter" class="form-label">
              <i class="bi bi-funnel me-2"></i>
              {% trans "Filter by Project" %}
            </label>
            <select class="form-select"
                    id="project-filter"
                    hx-get="{% url 'projects:kanban_board_htmx' %}"
                    hx-target="#board-container"
                    hx-indicator="#board-loading"
                    name="project_id">
              <option value="">{% trans "All Projects" %}</option>

              {% for project in projects %}
                <option value="{{ project.id }}" 
                  {% if project.id == project_id %}selected{% endif %}
                  >{{ project.name }}</option>
              {% endfor %}

            </select>
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Loading Indicator -->
    <div class="text-center py-4 d-none" id="board-loading">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{% trans "Loading..." %}</span>
      </div>
      <p class="mt-2 text-muted">{% trans "Loading task board..." %}</p>
    </div>
    <!-- Kanban Board Container -->
    <div id="board-container">
      <div class="kanban-board-container" id="kanban-view">{% include 'projects/partials/tasks/kanban_board.html' %}</div>
      <!-- List View Container (Initially Hidden) -->
      <div class="d-none" id="list-view">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="bi bi-list-ul me-2"></i>
              {% trans "Task List with Drag Handles" %}
            </h5>
          </div>
          <div class="card-body" id="task-list-sortable">
            <!-- List view will be loaded here -->
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Task Detail Modal -->
  <div class="modal fade"
       id="task-modal"
       tabindex="-1"
       aria-labelledby="taskModalLabel"
       aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <!-- Content loaded via HTMX -->
      </div>
    </div>
  </div>
  <!-- Task Detail Modal -->
  <div class="modal fade"
       id="task-detail-modal"
       tabindex="-1"
       aria-labelledby="taskDetailModalLabel"
       aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <!-- Content loaded via HTMX -->
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- SortableJS for drag and drop -->
  <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
  <script src="{% static 'js/features/task-drag-drop.js' %}"></script>
  <script>
document.addEventListener('DOMContentLoaded', function() {
    initializeDragAndDrop();
    initializeUndoRedo();
});

function initializeDragAndDrop() {
    // Initialize sortable for each kanban column
    const columns = document.querySelectorAll('[data-sortable-group="kanban-tasks"]');
    
    columns.forEach(column => {
        new Sortable(column, {
            group: 'kanban-tasks',
            animation: 150,
            fallbackOnBody: true,
            swapThreshold: 0.65,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            
            // Handle different types of drag operations
            onEnd: function(evt) {
                const taskId = evt.item.dataset.taskId;
                const newStatus = evt.to.dataset.status;
                const oldStatus = evt.from.dataset.status;
                const newIndex = evt.newIndex;
                
                // Determine the type of operation
                if (oldStatus !== newStatus) {
                    // Status change
                    handleStatusChange(taskId, oldStatus, newStatus);
                } else {
                    // Reorder within same column
                    handleReorder(taskId, newIndex);
                }
            },
            
            // Visual feedback during drag
            onMove: function(evt) {
                // Add visual indicators
                const related = evt.related;
                if (related && related.classList.contains('empty-column')) {
                    return false; // Don't allow dropping on empty state
                }
                return true;
            },
            
            // Touch support for mobile
            touchStartThreshold: 10,
            delayOnTouchStart: true,
            delay: 100
        });
    });
}

function handleStatusChange(taskId, oldStatus, newStatus) {
    // Show loading state
    showDragIndicator();
    
    // Send HTMX request for status change
    htmx.ajax('POST', '{% url "projects:task_drag_drop_htmx" %}', {
        values: {
            action: 'status_change',
            task_id: taskId,
            new_status: newStatus,
            old_status: oldStatus,
            csrfmiddlewaretoken: document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        target: '#drag-feedback',
        swap: 'innerHTML'
    }).then(() => {
        hideDragIndicator();
        // Refresh the board to show updated counts
        refreshKanbanBoard();
    }).catch(() => {
        hideDragIndicator();
        showError('Failed to update task status');
    });
}

function handleReorder(taskId, newOrder) {
    // Show loading state
    showDragIndicator();
    
    // Send HTMX request for reorder
    htmx.ajax('POST', '{% url "projects:task_drag_drop_htmx" %}', {
        values: {
            action: 'reorder',
            task_id: taskId,
            new_order: newOrder,
            csrfmiddlewaretoken: document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        target: '#drag-feedback',
        swap: 'innerHTML'
    }).then(() => {
        hideDragIndicator();
    }).catch(() => {
        hideDragIndicator();
        showError('Failed to reorder task');
    });
}

function refreshKanbanBoard() {
    const projectId = document.getElementById('project-filter')?.value || '';
    htmx.ajax('GET', '{% url "projects:kanban_board_htmx" %}', {
        values: { project_id: projectId },
        target: '#kanban-view',
        swap: 'innerHTML'
    }).then(() => {
        // Reinitialize drag and drop after refresh
        setTimeout(initializeDragAndDrop, 100);
    });
}

function toggleView(viewType) {
    const kanbanView = document.getElementById('kanban-view');
    const listView = document.getElementById('list-view');
    const kanbanBtn = document.getElementById('kanban-view-btn');
    const listBtn = document.getElementById('list-view-btn');
    
    if (viewType === 'kanban') {
        kanbanView.classList.remove('d-none');
        listView.classList.add('d-none');
        kanbanBtn.classList.add('active');
        listBtn.classList.remove('active');
    } else {
        kanbanView.classList.add('d-none');
        listView.classList.remove('d-none');
        kanbanBtn.classList.remove('active');
        listBtn.classList.add('active');
        
        // Load list view if not already loaded
        if (!listView.dataset.loaded) {
            loadListView();
        }
    }
}

function loadListView() {
    const projectId = document.getElementById('project-filter')?.value || '';
    const listContainer = document.getElementById('task-list-sortable');
    
    htmx.ajax('GET', '{% url "projects:task_list_htmx" %}', {
        values: { 
            project_id: projectId,
            view: 'sortable_list'
        },
        target: '#task-list-sortable',
        swap: 'innerHTML'
    }).then(() => {
        document.getElementById('list-view').dataset.loaded = 'true';
        initializeListSortable();
    });
}

function initializeListSortable() {
    const listContainer = document.getElementById('task-list-sortable');
    if (listContainer) {
        new Sortable(listContainer, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            
            onEnd: function(evt) {
                const taskId = evt.item.dataset.taskId;
                const newIndex = evt.newIndex;
                handleReorder(taskId, newIndex);
            }
        });
    }
}

function initializeUndoRedo() {
    // Initialize undo/redo functionality
    window.dragOperations = window.dragOperations || [];
    
    // Handle keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
            e.preventDefault();
            performUndo();
        } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
            e.preventDefault();
            performRedo();
        }
    });
}

function performUndo() {
    const undoableOperation = window.dragOperations.find(op => !op.undone);
    if (undoableOperation) {
        htmx.ajax('POST', '{% url "projects:task_undo_redo_htmx" %}', {
            values: {
                action: 'undo',
                operation_id: undoableOperation.id,
                csrfmiddlewaretoken: document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            target: '#undo-feedback',
            swap: 'innerHTML'
        });
    }
}

function performRedo() {
    const redoableOperation = window.dragOperations.find(op => op.undone);
    if (redoableOperation) {
        htmx.ajax('POST', '{% url "projects:task_undo_redo_htmx" %}', {
            values: {
                action: 'redo',
                operation_id: redoableOperation.id,
                csrfmiddlewaretoken: document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            target: '#undo-feedback',
            swap: 'innerHTML'
        });
    }
}

function showDragIndicator() {
    const indicator = document.getElementById('kanban-loading');
    if (indicator) {
        indicator.classList.remove('d-none');
    }
}

function hideDragIndicator() {
    const indicator = document.getElementById('kanban-loading');
    if (indicator) {
        indicator.classList.add('d-none');
    }
}

function showError(message) {
    // Create and show error toast
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

// Accessibility enhancements
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.target.classList.contains('task-card')) {
        e.target.click();
    }
});
  </script>
  <!-- Feedback containers for HTMX responses -->
  <div id="drag-feedback"></div>
  <div id="undo-feedback"></div>
  {% csrf_token %}
{% endblock %}
