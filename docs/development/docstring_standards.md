# Docstring Standards for CLEAR Project

This document defines the comprehensive docstring standards for the CLEAR Django project, following Google-style docstrings with project-specific enhancements.

## Overview

All public functions, classes, and modules in the CLEAR project must have comprehensive docstrings that follow Google-style conventions. This ensures consistent documentation, better IDE support, and improved maintainability.

## Configuration

The project uses `pydocstyle` for docstring linting with Google-style conventions configured in `pyproject.toml`:

```toml
[tool.pydocstyle]
convention = "google"
match = "(?!migrations|test_|conftest).*\\.py"
```

## Standards and Patterns

### 1. Module Docstrings

Every Python module should start with a comprehensive docstring:

```python
"""Project management models for the CLEAR platform.

This module contains Django models for managing construction projects,
including project lifecycle, team management, and spatial coordination
with utility infrastructure.

The models implement multi-tenant organization isolation and integrate
with PostGIS for spatial conflict detection and resolution.

Example:
    Basic usage of the Project model:
    
    >>> from apps.projects.models import Project
    >>> project = Project.objects.create(
    ...     name="Highway Expansion",
    ...     organization=my_org
    ... )
    >>> project.detect_utility_conflicts()

Note:
    All models in this module inherit from BaseModel which provides
    automatic timestamping and soft delete functionality.
"""
```

### 2. Class Docstrings

All classes must have comprehensive docstrings describing their purpose, attributes, and usage:

```python
class Project(BaseModel):
    """Represents a construction project with spatial coordination capabilities.
    
    The Project model is the central entity for managing construction projects
    within the CLEAR platform. It provides functionality for team management,
    timeline tracking, and spatial conflict detection with utility infrastructure.
    
    Attributes:
        name: Human-readable project name
        description: Detailed project description
        start_date: Planned project start date
        end_date: Planned project completion date
        geometry: PostGIS geometry field for project boundaries
        organization: Multi-tenant organization relationship
        status: Current project status (draft, active, completed, cancelled)
        
    Example:
        Creating a new project:
        
        >>> project = Project.objects.create(
        ...     name="Main Street Reconstruction",
        ...     description="Complete reconstruction of Main Street",
        ...     organization=organization,
        ...     start_date=date(2024, 6, 1)
        ... )
        >>> project.detect_utility_conflicts()
        
    Note:
        All spatial operations use SRID 4326 (WGS84) coordinate system.
        Projects are automatically filtered by organization for multi-tenancy.
    """
```

### 3. Function and Method Docstrings

All public functions and methods must have detailed docstrings:

```python
def detect_utility_conflicts(
    self,
    buffer_distance: float = 5.0,
    utility_types: Optional[List[str]] = None
) -> QuerySet[UtilityConflict]:
    """Detect potential conflicts between project geometry and existing utilities.
    
    This method performs spatial analysis to identify utility infrastructure
    that may conflict with the planned project construction. It uses PostGIS
    spatial queries to find utilities within the specified buffer distance.
    
    Args:
        buffer_distance: Distance in meters to buffer project geometry for
            conflict detection. Defaults to 5.0 meters.
        utility_types: Optional list of utility types to check. If None,
            all utility types are checked. Valid types include 'water',
            'sewer', 'gas', 'electric', 'telecom'.
            
    Returns:
        QuerySet of UtilityConflict objects representing potential conflicts,
        ordered by distance from project geometry.
        
    Raises:
        ValidationError: If project geometry is invalid or missing.
        ValueError: If buffer_distance is negative or utility_types contains
            invalid utility type names.
            
    Example:
        Detect all utility conflicts within 10 meters:
        
        >>> conflicts = project.detect_utility_conflicts(buffer_distance=10.0)
        >>> for conflict in conflicts:
        ...     print(f"Conflict with {conflict.utility.name}")
        
        Detect only water and sewer conflicts:
        
        >>> conflicts = project.detect_utility_conflicts(
        ...     utility_types=['water', 'sewer']
        ... )
        
    Note:
        This method requires the project to have valid geometry data.
        Results are cached for 15 minutes to improve performance.
    """
```

### 4. Property Docstrings

Properties should have concise but informative docstrings:

```python
@property
def completion_percentage(self) -> float:
    """Calculate project completion percentage based on completed tasks.
    
    Returns:
        Float between 0.0 and 100.0 representing completion percentage.
        Returns 0.0 if project has no tasks.
        
    Example:
        >>> project.completion_percentage
        75.5
    """
```

### 5. Django View Docstrings

Django views should document their purpose, permissions, and context:

```python
class ProjectDetailView(LoginRequiredMixin, DetailView):
    """Display detailed information about a specific project.
    
    This view shows comprehensive project information including tasks,
    team members, timeline, and spatial data. It requires user authentication
    and organization-level access to the project.
    
    Template: projects/detail.html
    Context:
        project: The Project instance being displayed
        tasks: QuerySet of active tasks for the project
        team_members: List of users assigned to the project
        conflicts: QuerySet of unresolved utility conflicts
        
    Permissions:
        - User must be authenticated
        - User must belong to the project's organization
        - User must have 'view_project' permission
        
    Example:
        URL: /projects/123/
        Template context includes project data and related objects.
    """
```

### 6. Django Form Docstrings

Forms should document their purpose, fields, and validation:

```python
class ProjectForm(forms.ModelForm):
    """Form for creating and editing Project instances.
    
    This form handles project creation and updates with custom validation
    for spatial data and business rules. It includes client-side validation
    for improved user experience.
    
    Fields:
        name: Required project name (max 255 characters)
        description: Optional project description
        start_date: Required project start date
        end_date: Optional project end date (must be after start_date)
        geometry: Optional project boundary geometry
        
    Validation:
        - Name must be unique within organization
        - End date must be after start date
        - Geometry must be valid PostGIS geometry if provided
        
    Example:
        >>> form = ProjectForm(data={
        ...     'name': 'New Project',
        ...     'start_date': '2024-06-01'
        ... })
        >>> if form.is_valid():
        ...     project = form.save(commit=False)
        ...     project.organization = request.user.organization
        ...     project.save()
    """
```

### 7. Service Class Docstrings

Service classes should document their purpose and methods:

```python
class ProjectService:
    """Service class for project-related business logic.
    
    This service encapsulates complex business logic for project management,
    including creation, updates, conflict detection, and reporting. It provides
    a clean interface between views and models.
    
    Attributes:
        user: The authenticated user performing operations
        organization: The user's organization for multi-tenant filtering
        
    Example:
        >>> service = ProjectService(user=request.user, organization=org)
        >>> project = service.create_project({
        ...     'name': 'Highway Project',
        ...     'start_date': date.today()
        ... })
        >>> conflicts = service.analyze_conflicts(project)
    """
```

## Docstring Components

### Required Components

1. **Summary Line**: One-line description of the function/class
2. **Description**: Detailed explanation of purpose and behavior
3. **Args**: All parameters with types and descriptions
4. **Returns**: Return value type and description
5. **Raises**: Exceptions that may be raised

### Optional Components

1. **Example**: Code examples showing usage
2. **Note**: Important implementation details
3. **Warning**: Critical warnings about usage
4. **See Also**: References to related functions/classes
5. **Todo**: Future improvements or known issues

### Django-Specific Components

1. **Template**: Template file used (for views)
2. **Context**: Template context variables (for views)
3. **Permissions**: Required permissions (for views)
4. **Fields**: Form fields and validation (for forms)
5. **Validation**: Custom validation rules (for forms)

## Best Practices

### 1. Be Specific and Accurate

```python
# Good
def calculate_distance(point1: Coordinates, point2: Coordinates) -> float:
    """Calculate the Euclidean distance between two geographic points.
    
    Args:
        point1: First point as (longitude, latitude) tuple in WGS84
        point2: Second point as (longitude, latitude) tuple in WGS84
        
    Returns:
        Distance in meters between the two points
    """

# Avoid
def calculate_distance(point1, point2):
    """Calculate distance between points."""
```

### 2. Include Examples

```python
def format_project_status(status: str, include_icon: bool = False) -> str:
    """Format project status for display.
    
    Args:
        status: Raw status value ('draft', 'active', 'completed', 'cancelled')
        include_icon: Whether to include status icon in output
        
    Returns:
        Formatted status string ready for display
        
    Example:
        >>> format_project_status('active')
        'Active'
        >>> format_project_status('active', include_icon=True)
        '🟢 Active'
    """
```

### 3. Document Edge Cases

```python
def get_project_progress(project: Project) -> Dict[str, Any]:
    """Calculate comprehensive project progress metrics.
    
    Args:
        project: Project instance to analyze
        
    Returns:
        Dictionary containing progress metrics:
        - completion_percentage: Float 0-100
        - tasks_completed: Integer count
        - tasks_total: Integer count
        - days_remaining: Integer (negative if overdue)
        
    Note:
        Returns zero values if project has no tasks.
        Days remaining calculation uses project end_date if set,
        otherwise estimates based on current progress rate.
    """
```

## Tools and Automation

### 1. Pydocstyle Integration

Run docstring linting:

```bash
pydocstyle apps/ config/
```

### 2. IDE Integration

Configure your IDE to show docstring warnings:
- PyCharm: Enable pydocstyle inspection
- VS Code: Install Python Docstring Generator extension

### 3. Pre-commit Hooks

Add pydocstyle to pre-commit configuration:

```yaml
repos:
  - repo: local
    hooks:
      - id: pydocstyle
        name: pydocstyle
        entry: pydocstyle
        language: system
        files: \.py$
        exclude: ^(migrations|tests)/
```

## Migration Strategy

### Phase 1: Core Infrastructure ✅
- Enhanced pydocstyle configuration
- Created comprehensive docstring standards
- Updated pyproject.toml with Google-style conventions

### Phase 2: Models and Core Classes
- Add comprehensive docstrings to all Django models
- Document custom managers and querysets
- Enhance service class documentation

### Phase 3: Views and Forms
- Add detailed docstrings to all view classes and functions
- Document form classes and validation methods
- Enhance template context documentation

### Phase 4: Utilities and Helpers
- Document all utility functions and helper classes
- Add examples and usage patterns
- Enhance API documentation

This comprehensive docstring standard ensures consistent, high-quality documentation throughout the CLEAR project, improving code maintainability and developer experience.
