# Error Handling Standards for CLEAR Project

This guide provides comprehensive standards and best practices for error handling throughout the CLEAR Django project, ensuring consistent error management, logging, and user experience.

## Overview

The CLEAR project uses a standardized exception hierarchy and error handling patterns to provide:

- Consistent error structure across all applications
- Comprehensive error logging with context
- User-friendly error messages
- Structured error categorization and severity levels
- Integration with monitoring and alerting systems

## Exception Hierarchy

All custom exceptions inherit from `ClearBaseException`, which provides:

```python
from apps.core.exceptions import ClearBaseException, ValidationError, BusinessRuleError

# Base exception with full context
class ClearBaseException(Exception):
    - error_id: Unique identifier for tracking
    - error_code: Categorization code
    - message: Technical message for developers
    - user_message: User-friendly message
    - severity: Error severity level
    - category: Error category
    - details: Additional context
    - timestamp: When the error occurred
```

## Error Categories and Severity

### Categories

- **VALIDATION**: Data validation failures
- **AUTHENTICATION**: Authentication failures
- **AUTHORIZATION**: Permission/access issues
- **BUSINESS_LOGIC**: Business rule violations
- **EXTERNAL_SERVICE**: Third-party service failures
- **SYSTEM**: System-level errors
- **SPATIAL**: PostGIS/spatial operation errors
- **WORKFLOW**: Process/workflow errors

### Severity Levels

- **LOW**: Minor issues, system continues normally
- **MEDIUM**: Moderate issues, user action may be required
- **HIGH**: Significant issues, immediate attention needed
- **CRITICAL**: System-threatening issues, immediate intervention required

## Standard Exception Classes

### 1. Validation Errors

```python
from apps.core.exceptions import ValidationError

# Basic validation error
raise ValidationError("Email format is invalid")

# With user-friendly message
raise ValidationError(
    "Email validation failed: invalid format",
    user_message="Please enter a valid email address",
    details={'field': 'email', 'value': 'invalid@'}
)

# With field-specific context
raise ValidationError(
    "Project name must be unique within organization",
    details={
        'field': 'name',
        'value': 'Duplicate Project',
        'organization_id': 123,
        'existing_project_id': 456
    }
)
```

### 2. Business Rule Errors

```python
from apps.core.exceptions import BusinessRuleError

# Business rule violation
raise BusinessRuleError(
    "Cannot delete project with active tasks",
    user_message="This project has active tasks and cannot be deleted. Please complete or reassign all tasks first.",
    details={
        'project_id': project.id,
        'active_task_count': 5,
        'active_task_ids': [1, 2, 3, 4, 5]
    }
)
```

### 3. Authentication/Authorization Errors

```python
from apps.core.exceptions import AuthenticationError, AuthorizationError

# Authentication failure
raise AuthenticationError(
    "Invalid credentials provided",
    user_message="Invalid email or password. Please try again."
)

# Authorization failure
raise AuthorizationError(
    "User lacks permission to access project",
    user_message="You don't have permission to access this project.",
    details={
        'user_id': user.id,
        'project_id': project.id,
        'required_permission': 'view_project'
    }
)
```

### 4. Spatial/PostGIS Errors

```python
from apps.core.exceptions import SpatialError

# Spatial operation error
raise SpatialError(
    "Geometry intersection calculation failed",
    user_message="Unable to calculate spatial conflicts. Please check the project boundaries.",
    details={
        'geometry_type': 'Polygon',
        'srid': 4326,
        'operation': 'intersection'
    }
)
```

## Error Handling Patterns

### 1. View Error Handling

```python
from django.http import HttpRequest, HttpResponse
from django.shortcuts import render
from apps.core.exceptions import ValidationError, BusinessRuleError
from apps.core.utils.error_handlers import handle_view_error

def project_create_view(request: HttpRequest) -> HttpResponse:
    """Create a new project with comprehensive error handling."""
    try:
        # Business logic here
        project = create_project(request.POST)
        return redirect('projects:detail', pk=project.pk)
        
    except ValidationError as e:
        return handle_view_error(request, e, 'projects/create.html')
        
    except BusinessRuleError as e:
        return handle_view_error(request, e, 'projects/create.html')
        
    except Exception as e:
        # Convert unexpected errors to CLEAR exceptions
        clear_error = ClearBaseException(
            f"Unexpected error in project creation: {e}",
            error_code="UNEXPECTED_ERROR",
            severity=ErrorSeverity.HIGH,
            details={'original_error': str(e)}
        )
        return handle_view_error(request, clear_error, 'projects/create.html')
```

### 2. API Error Handling

```python
from rest_framework.decorators import api_view
from rest_framework.response import Response
from apps.core.utils.error_handlers import handle_api_error

@api_view(['POST'])
def create_project_api(request):
    """API endpoint with standardized error handling."""
    try:
        # Business logic here
        project = create_project(request.data)
        return Response({'project_id': project.id}, status=201)
        
    except ValidationError as e:
        return handle_api_error(e)
        
    except BusinessRuleError as e:
        return handle_api_error(e)
        
    except Exception as e:
        clear_error = ClearBaseException(
            f"API error in project creation: {e}",
            error_code="API_ERROR",
            severity=ErrorSeverity.HIGH
        )
        return handle_api_error(clear_error)
```

### 3. Service Layer Error Handling

```python
from typing import Optional
from apps.core.exceptions import ValidationError, BusinessRuleError

class ProjectService:
    """Service class with comprehensive error handling."""
    
    def create_project(self, data: dict, user: User) -> Project:
        """Create project with validation and business rule checking."""
        try:
            # Validate input data
            self._validate_project_data(data)
            
            # Check business rules
            self._check_project_creation_rules(data, user)
            
            # Create project
            project = Project.objects.create(**data, created_by=user)
            
            return project
            
        except ValidationError:
            # Re-raise validation errors as-is
            raise
            
        except BusinessRuleError:
            # Re-raise business rule errors as-is
            raise
            
        except Exception as e:
            # Convert unexpected errors
            raise ClearBaseException(
                f"Project creation failed: {e}",
                error_code="PROJECT_CREATION_ERROR",
                severity=ErrorSeverity.HIGH,
                details={
                    'user_id': user.id,
                    'project_data': data,
                    'original_error': str(e)
                }
            ) from e
    
    def _validate_project_data(self, data: dict) -> None:
        """Validate project data."""
        if not data.get('name'):
            raise ValidationError(
                "Project name is required",
                user_message="Please provide a project name",
                details={'field': 'name'}
            )
        
        if len(data.get('name', '')) > 255:
            raise ValidationError(
                "Project name too long",
                user_message="Project name must be 255 characters or less",
                details={'field': 'name', 'max_length': 255}
            )
    
    def _check_project_creation_rules(self, data: dict, user: User) -> None:
        """Check business rules for project creation."""
        # Check user's project limit
        user_project_count = Project.objects.filter(created_by=user).count()
        max_projects = user.organization.max_projects_per_user
        
        if user_project_count >= max_projects:
            raise BusinessRuleError(
                f"User has reached maximum project limit: {max_projects}",
                user_message=f"You have reached your maximum limit of {max_projects} projects. Please contact your administrator to increase your limit.",
                details={
                    'current_count': user_project_count,
                    'max_allowed': max_projects,
                    'user_id': user.id
                }
            )
```

## Error Handler Utilities

### 1. View Error Handler

```python
# apps/core/utils/error_handlers.py
from django.http import HttpRequest, HttpResponse
from django.shortcuts import render
from django.contrib import messages
from apps.core.exceptions import ClearBaseException

def handle_view_error(
    request: HttpRequest, 
    error: ClearBaseException, 
    template: str,
    context: dict = None
) -> HttpResponse:
    """Handle errors in Django views with user-friendly messages."""
    
    # Add error message for user
    if error.is_user_error():
        messages.error(request, error.user_message)
    else:
        messages.error(request, "An unexpected error occurred. Please try again.")
    
    # Add error context for debugging (in development)
    error_context = {}
    if settings.DEBUG:
        error_context = {
            'error_id': error.error_id,
            'error_code': error.error_code,
            'error_details': error.details
        }
    
    # Merge with existing context
    final_context = context or {}
    final_context.update(error_context)
    
    return render(request, template, final_context)
```

### 2. API Error Handler

```python
from rest_framework.response import Response
from apps.core.exceptions import ClearBaseException

def handle_api_error(error: ClearBaseException) -> Response:
    """Handle errors in API views with structured JSON responses."""
    
    # Determine HTTP status code based on error type
    status_code = 500  # Default
    
    if error.category == ErrorCategory.VALIDATION:
        status_code = 400
    elif error.category == ErrorCategory.AUTHENTICATION:
        status_code = 401
    elif error.category == ErrorCategory.AUTHORIZATION:
        status_code = 403
    elif error.category == ErrorCategory.BUSINESS_LOGIC:
        status_code = 422
    
    return Response(error.to_json_response(), status=status_code)
```

## Best Practices

### 1. Always Use Specific Exceptions

```python
# Good
raise ValidationError("Email format is invalid")

# Avoid
raise Exception("Email format is invalid")
```

### 2. Provide Context

```python
# Good
raise BusinessRuleError(
    "Cannot delete project with active tasks",
    details={
        'project_id': project.id,
        'active_task_count': tasks.count()
    }
)

# Avoid
raise BusinessRuleError("Cannot delete project")
```

### 3. Use User-Friendly Messages

```python
# Good
raise ValidationError(
    "Email validation failed: invalid format",
    user_message="Please enter a valid email address"
)

# Avoid
raise ValidationError("Email regex match failed")
```

### 4. Chain Exceptions

```python
# Good
try:
    external_api_call()
except ExternalAPIError as e:
    raise ExternalServiceError(
        "Failed to sync with external service",
        details={'service': 'project_api', 'error': str(e)}
    ) from e
```

## Integration with Monitoring

The error handling system integrates with monitoring tools:

- **Error IDs**: Unique identifiers for tracking across systems
- **Structured Logging**: JSON-formatted logs for analysis
- **Severity Levels**: For alerting and escalation
- **Categories**: For error classification and routing

## Testing Error Handling

```python
import pytest
from apps.core.exceptions import ValidationError

def test_project_creation_validation():
    """Test project creation validation errors."""
    with pytest.raises(ValidationError) as exc_info:
        create_project({'name': ''})
    
    error = exc_info.value
    assert error.error_code == "VALIDATION_ERROR"
    assert error.category == ErrorCategory.VALIDATION
    assert 'field' in error.details
    assert error.details['field'] == 'name'
```

This comprehensive error handling system ensures consistent, user-friendly, and maintainable error management throughout the CLEAR project.
