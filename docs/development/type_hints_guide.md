# Type Hints Guide for CLEAR Project

This guide provides comprehensive standards and best practices for using type hints throughout the CLEAR Django project.

## Overview

The CLEAR project uses comprehensive type hints to improve code quality, IDE support, and maintainability. All new code must include proper type annotations, and existing code is being gradually enhanced with type hints.

## Configuration

### MyPy Configuration

The project uses MyPy for static type checking with strict settings in `mypy.ini`:

```ini
[mypy]
python_version = 3.12
strict = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
show_error_codes = True
```

### Type Definitions

Common type definitions are centralized in `apps/core/types.py`:

```python
from apps.core.types import (
    DjangoRequest,
    DjangoResponse,
    JSONDict,
    ContextData,
    PrimaryKey,
    ModelType,
)
```

## Standards and Patterns

### 1. Import Standards

Always use `from __future__ import annotations` for forward references:

```python
from __future__ import annotations

from typing import Any, Dict, List, Optional, Union
from django.http import HttpRequest, HttpResponse
from apps.core.types import DjangoRequest, <PERSON><PERSON><PERSON>ict
```

### 2. Function Type Hints

All functions must have complete type annotations:

```python
def process_project_data(
    request: HttpRequest,
    project_id: int,
    filters: Optional[Dict[str, Any]] = None
) -> HttpResponse:
    """Process project data with optional filters."""
    # Implementation here
    pass
```

### 3. Class Type Hints

#### Django Models

```python
class Project(BaseModel):
    """Project model with comprehensive type hints."""
    
    name: models.CharField = models.CharField(max_length=255)
    description: models.TextField = models.TextField(blank=True)
    
    objects: ClassVar[BaseOptimizedManager] = BaseOptimizedManager()
    
    def __str__(self) -> str:
        return self.name
    
    def get_absolute_url(self) -> str:
        return reverse('projects:detail', kwargs={'pk': self.pk})
```

#### Django Views

```python
class ProjectDetailView(LoginRequiredMixin, DetailView[Project]):
    """Project detail view with type hints."""
    
    model = Project
    template_name: str = 'projects/detail.html'
    context_object_name: str = 'project'
    
    def get_context_data(self, **kwargs: Any) -> ContextData:
        context = super().get_context_data(**kwargs)
        context['tasks'] = self.object.tasks.active()
        return context
```

#### Django Forms

```python
class ProjectForm(forms.ModelForm[Project]):
    """Project form with type hints."""
    
    class Meta:
        model = Project
        fields: List[str] = ['name', 'description', 'start_date']
    
    def clean_name(self) -> str:
        name = self.cleaned_data['name']
        if len(name) < 3:
            raise forms.ValidationError("Name must be at least 3 characters")
        return name
```

### 4. Manager and QuerySet Type Hints

```python
class ProjectQuerySet(models.QuerySet[Project]):
    """Typed QuerySet for Project model."""
    
    def active(self) -> ProjectQuerySet:
        return self.filter(is_active=True)
    
    def by_organization(self, org_id: int) -> ProjectQuerySet:
        return self.filter(organization_id=org_id)


class ProjectManager(models.Manager[Project]):
    """Typed Manager for Project model."""
    
    def get_queryset(self) -> ProjectQuerySet:
        return ProjectQuerySet(self.model, using=self._db)
    
    def active(self) -> ProjectQuerySet:
        return self.get_queryset().active()
```

### 5. Service Layer Type Hints

```python
class ProjectService:
    """Service for project operations with type hints."""
    
    def __init__(self, user: User, organization: Organization) -> None:
        self.user = user
        self.organization = organization
    
    def create_project(self, data: JSONDict) -> Project:
        """Create a new project."""
        project = Project.objects.create(
            organization=self.organization,
            created_by=self.user,
            **data
        )
        return project
    
    def get_user_projects(self) -> QuerySet[Project]:
        """Get projects accessible to the user."""
        return Project.objects.filter(
            organization=self.organization
        ).active()
```

### 6. Utility Functions

```python
def calculate_project_progress(
    project: Project,
    include_subtasks: bool = True
) -> Tuple[int, int]:
    """Calculate project progress as (completed, total) tasks.
    
    Args:
        project: The project to analyze
        include_subtasks: Whether to include subtask counts
        
    Returns:
        Tuple of (completed_count, total_count)
    """
    queryset = project.tasks.all()
    if include_subtasks:
        queryset = queryset.prefetch_related('subtasks')
    
    total = queryset.count()
    completed = queryset.filter(status='completed').count()
    
    return completed, total
```

## Common Type Patterns

### 1. Django Request/Response

```python
# Function-based views
def project_list(request: HttpRequest) -> HttpResponse:
    return render(request, 'projects/list.html')

# HTMX views
def project_partial(request: HttpRequest) -> HttpResponse:
    if request.htmx:
        template = 'projects/partials/list.html'
    else:
        template = 'projects/list.html'
    return render(request, template)
```

### 2. Optional and Union Types

```python
def get_project_by_id(
    project_id: int,
    organization: Optional[Organization] = None
) -> Optional[Project]:
    """Get project by ID with optional organization filter."""
    try:
        queryset = Project.objects.all()
        if organization:
            queryset = queryset.filter(organization=organization)
        return queryset.get(id=project_id)
    except Project.DoesNotExist:
        return None
```

### 3. Generic Types

```python
from typing import Generic, TypeVar

T = TypeVar('T', bound=models.Model)

class BaseService(Generic[T]):
    """Generic base service class."""
    
    model_class: Type[T]
    
    def __init__(self, model_class: Type[T]) -> None:
        self.model_class = model_class
    
    def get_all(self) -> QuerySet[T]:
        return self.model_class.objects.all()
```

### 4. Protocol Types

```python
from typing import Protocol

class HasOrganization(Protocol):
    """Protocol for models with organization."""
    organization: Organization
    organization_id: int

def filter_by_organization(
    queryset: QuerySet[HasOrganization],
    organization: Organization
) -> QuerySet[HasOrganization]:
    """Filter queryset by organization."""
    return queryset.filter(organization=organization)
```

## Best Practices

### 1. Use Specific Types

```python
# Good
def get_project_names() -> List[str]:
    return list(Project.objects.values_list('name', flat=True))

# Avoid
def get_project_names() -> List[Any]:
    return list(Project.objects.values_list('name', flat=True))
```

### 2. Handle None Values

```python
# Good
def get_project_description(project: Optional[Project]) -> str:
    if project is None:
        return "No project"
    return project.description or "No description"

# Avoid
def get_project_description(project):
    return project.description
```

### 3. Use ClassVar for Class Variables

```python
class ProjectConfig:
    """Project configuration with proper class variable typing."""
    
    DEFAULT_STATUS: ClassVar[str] = 'draft'
    VALID_STATUSES: ClassVar[List[str]] = ['draft', 'active', 'completed']
    
    def __init__(self, status: str = DEFAULT_STATUS) -> None:
        self.status = status
```

## Tools and Automation

### 1. Type Hint Enhancement Script

Run the automated type hint enhancement script:

```bash
python scripts/enhance_type_hints.py
```

### 2. MyPy Checking

Run type checking:

```bash
mypy apps/ config/
```

### 3. IDE Integration

Configure your IDE to use the project's type stubs:
- PyCharm: Enable Django support and configure Python interpreter
- VS Code: Install Python extension and configure mypy

## Migration Strategy

### Phase 1: Core Infrastructure ✅
- Enhanced `apps/core/types.py` with comprehensive type definitions
- Updated mypy configuration for strict checking
- Enhanced base models and managers

### Phase 2: Models and Managers
- Add type hints to all Django models
- Enhance custom managers and querysets
- Update model methods and properties

### Phase 3: Views and Forms
- Add type hints to all view functions and classes
- Enhance form classes and validation methods
- Update template context handling

### Phase 4: Services and Utilities
- Add type hints to service layer classes
- Enhance utility functions and helpers
- Update API serializers and viewsets

### Phase 5: Testing and Validation
- Run comprehensive mypy checks
- Fix any type errors
- Update documentation and examples

## Common Issues and Solutions

### 1. Circular Imports

Use string literals for forward references:

```python
class Project(models.Model):
    parent: Optional['Project'] = models.ForeignKey(
        'self', null=True, blank=True, on_delete=models.CASCADE
    )
```

### 2. Django Model Fields

Use proper field type annotations:

```python
class Project(models.Model):
    name: models.CharField = models.CharField(max_length=255)
    created_at: models.DateTimeField = models.DateTimeField(auto_now_add=True)
```

### 3. QuerySet Typing

Use generic QuerySet types:

```python
def get_active_projects() -> QuerySet[Project]:
    return Project.objects.filter(is_active=True)
```

This guide ensures consistent and comprehensive type hint usage throughout the CLEAR project, improving code quality and developer experience.
