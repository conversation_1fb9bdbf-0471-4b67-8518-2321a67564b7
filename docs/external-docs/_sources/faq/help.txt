=================
FAQ: Getting Help
=================

How do I do X? Why doesn't Y work? Where can I go to get help?
==============================================================

First, please check if your question is answered on the :doc:`FAQ
</faq/index>`. Also, search for answers using your favorite search engine, and
in `the forum`_.

.. _`the forum`: https://forum.djangoproject.com/

If you can't find an answer, please take a few minutes to formulate your
question well. Explaining the problems you are facing clearly will help others
help you. See the StackOverflow guide on `asking good questions`_.

.. _`asking good questions`: https://stackoverflow.com/help/how-to-ask

Then, please post it in one of the following channels:

* The Django Forum section `"Using Django"`_. This is for web-based
  discussions.
* The `Django Discord server`_ for chat-based discussions.

.. _`"Using Django"`: https://forum.djangoproject.com/c/users/6
.. _`Django Discord server`: https://chat.djangoproject.com

In all these channels please abide by the `Django Code of Conduct`_. In
summary, being friendly and patient, considerate, respectful, and careful in
your choice of words.

.. _Django Code of Conduct: https://www.djangoproject.com/conduct/

Nobody answered my question! What should I do?
==============================================

Try making your question more specific, or provide a better example of your
problem.

As with most open-source projects, the folks on these channels are volunteers.
If nobody has answered your question, it may be because nobody knows the
answer, it may be because nobody can understand the question, or it may be that
everybody that can help is busy.

You can also try asking on a different channel. But please don't post your
question in all three channels in quick succession.

I think I've found a bug! What should I do?
===========================================

Detailed instructions on how to handle a potential bug can be found in our
:ref:`Guide to contributing to Django <reporting-bugs>`.

I think I've found a security problem! What should I do?
========================================================

If you think you've found a security problem with Django, please send a message
to <EMAIL>. This is a private list only open to long-time,
highly trusted Django developers, and its archives are not publicly readable.

Due to the sensitive nature of security issues, we ask that if you think you
have found a security problem, *please* don't post a message on the forum, the
Discord server, IRC, or one of the public mailing lists. Django has a
:ref:`policy for handling security issues <reporting-security-issues>`;
while a defect is outstanding, we would like to minimize any damage that
could be inflicted through public knowledge of that defect.
