<!DOCTYPE html>

<html lang="en" data-content_root="../../../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>PostgreSQL specific model fields &#8212; Django 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=34bb78ad" />
    <link rel="stylesheet" type="text/css" href="../../../_static/default.css?v=bf4d74af" />
    <script src="../../../_static/documentation_options.js?v=6593ad68"></script>
    <script src="../../../_static/doctools.js?v=9bcbadda"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
    <link rel="next" title="PostgreSQL specific form fields and widgets" href="forms.html" />
    <link rel="prev" title="PostgreSQL specific query expressions" href="expressions.html" />



 
<script src="../../../templatebuiltins.js"></script>
<script>
(function($) {
    if (!django_template_builtins) {
       // templatebuiltins.js missing, do nothing.
       return;
    }
    $(document).ready(function() {
        // Hyperlink Django template tags and filters
        var base = "../../templates/builtins.html";
        if (base == "#") {
            // Special case for builtins.html itself
            base = "";
        }
        // Tags are keywords, class '.k'
        $("div.highlight\\-html\\+django span.k").each(function(i, elem) {
             var tagname = $(elem).text();
             if ($.inArray(tagname, django_template_builtins.ttags) != -1) {
                 var fragment = tagname.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + tagname + "</a>");
             }
        });
        // Filters are functions, class '.nf'
        $("div.highlight\\-html\\+django span.nf").each(function(i, elem) {
             var filtername = $(elem).text();
             if ($.inArray(filtername, django_template_builtins.tfilters) != -1) {
                 var fragment = filtername.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + filtername + "</a>");
             }
        });
    });
})(jQuery);</script>

  </head><body>

    <div class="document">
  <div id="custom-doc" class="yui-t6">
    <div id="hd">
      <h1><a href="../../../index.html">Django 5.2 documentation</a></h1>
      <div id="global-nav">
        <a title="Home page" href="../../../index.html">Home</a>  |
        <a title="Table of contents" href="../../../contents.html">Table of contents</a>  |
        <a title="Global index" href="../../../genindex.html">Index</a>  |
        <a title="Module index" href="../../../py-modindex.html">Modules</a>
      </div>
      <div class="nav">
    &laquo; <a href="expressions.html" title="PostgreSQL specific query expressions">previous</a>
     |
    <a href="../../index.html" title="API Reference" accesskey="U">up</a>
   |
    <a href="forms.html" title="PostgreSQL specific form fields and widgets">next</a> &raquo;</div>
    </div>

    <div id="bd">
      <div id="yui-main">
        <div class="yui-b">
          <div class="yui-g" id="ref-contrib-postgres-fields">
            
  <section id="s-postgresql-specific-model-fields">
<span id="postgresql-specific-model-fields"></span><h1>PostgreSQL specific model fields<a class="headerlink" href="#postgresql-specific-model-fields" title="Link to this heading">¶</a></h1>
<p>All of these fields are available from the <code class="docutils literal notranslate"><span class="pre">django.contrib.postgres.fields</span></code>
module.</p>
<section id="s-indexing-these-fields">
<span id="indexing-these-fields"></span><h2>Indexing these fields<a class="headerlink" href="#indexing-these-fields" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="../../models/indexes.html#django.db.models.Index" title="django.db.models.Index"><code class="xref py py-class docutils literal notranslate"><span class="pre">Index</span></code></a> and <a class="reference internal" href="../../models/fields.html#django.db.models.Field.db_index" title="django.db.models.Field.db_index"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Field.db_index</span></code></a> both create a
B-tree index, which isn’t particularly helpful when querying complex data types.
Indexes such as <a class="reference internal" href="indexes.html#django.contrib.postgres.indexes.GinIndex" title="django.contrib.postgres.indexes.GinIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">GinIndex</span></code></a> and
<a class="reference internal" href="indexes.html#django.contrib.postgres.indexes.GistIndex" title="django.contrib.postgres.indexes.GistIndex"><code class="xref py py-class docutils literal notranslate"><span class="pre">GistIndex</span></code></a> are better suited, though
the index choice is dependent on the queries that you’re using. Generally, GiST
may be a good choice for the <a class="reference internal" href="#range-fields"><span class="std std-ref">range fields</span></a> and
<a class="reference internal" href="#django.contrib.postgres.fields.HStoreField" title="django.contrib.postgres.fields.HStoreField"><code class="xref py py-class docutils literal notranslate"><span class="pre">HStoreField</span></code></a>, and GIN may be helpful for <a class="reference internal" href="#django.contrib.postgres.fields.ArrayField" title="django.contrib.postgres.fields.ArrayField"><code class="xref py py-class docutils literal notranslate"><span class="pre">ArrayField</span></code></a>.</p>
</section>
<section id="s-arrayfield">
<span id="arrayfield"></span><h2><code class="docutils literal notranslate"><span class="pre">ArrayField</span></code><a class="headerlink" href="#arrayfield" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.ArrayField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">ArrayField</span></span>(<em class="sig-param"><span class="n"><span class="pre">base_field</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.ArrayField" title="Link to this definition">¶</a></dt>
<dd><p>A field for storing lists of data. Most field types can be used, and you
pass another field instance as the <a class="reference internal" href="#django.contrib.postgres.fields.ArrayField.base_field" title="django.contrib.postgres.fields.ArrayField.base_field"><code class="xref py py-attr docutils literal notranslate"><span class="pre">base_field</span></code></a>. You may also specify a <a class="reference internal" href="#django.contrib.postgres.fields.ArrayField.size" title="django.contrib.postgres.fields.ArrayField.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">size</span></code></a>. <code class="docutils literal notranslate"><span class="pre">ArrayField</span></code> can be nested to store multi-dimensional
arrays.</p>
<p>If you give the field a <a class="reference internal" href="../../models/fields.html#django.db.models.Field.default" title="django.db.models.Field.default"><code class="xref py py-attr docutils literal notranslate"><span class="pre">default</span></code></a>, ensure
it’s a callable such as <code class="docutils literal notranslate"><span class="pre">list</span></code> (for an empty default) or a callable that
returns a list (such as a function). Incorrectly using <code class="docutils literal notranslate"><span class="pre">default=[]</span></code>
creates a mutable default that is shared between all instances of
<code class="docutils literal notranslate"><span class="pre">ArrayField</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.ArrayField.base_field">
<span class="sig-name descname"><span class="pre">base_field</span></span><a class="headerlink" href="#django.contrib.postgres.fields.ArrayField.base_field" title="Link to this definition">¶</a></dt>
<dd><p>This is a required argument.</p>
<p>Specifies the underlying data type and behavior for the array. It
should be an instance of a subclass of
<a class="reference internal" href="../../models/fields.html#django.db.models.Field" title="django.db.models.Field"><code class="xref py py-class docutils literal notranslate"><span class="pre">Field</span></code></a>. For example, it could be an
<a class="reference internal" href="../../models/fields.html#django.db.models.IntegerField" title="django.db.models.IntegerField"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntegerField</span></code></a> or a
<a class="reference internal" href="../../models/fields.html#django.db.models.CharField" title="django.db.models.CharField"><code class="xref py py-class docutils literal notranslate"><span class="pre">CharField</span></code></a>. Most field types are permitted,
with the exception of those handling relational data
(<a class="reference internal" href="../../models/fields.html#django.db.models.ForeignKey" title="django.db.models.ForeignKey"><code class="xref py py-class docutils literal notranslate"><span class="pre">ForeignKey</span></code></a>,
<a class="reference internal" href="../../models/fields.html#django.db.models.OneToOneField" title="django.db.models.OneToOneField"><code class="xref py py-class docutils literal notranslate"><span class="pre">OneToOneField</span></code></a> and
<a class="reference internal" href="../../models/fields.html#django.db.models.ManyToManyField" title="django.db.models.ManyToManyField"><code class="xref py py-class docutils literal notranslate"><span class="pre">ManyToManyField</span></code></a>) and file fields
(<a class="reference internal" href="../../models/fields.html#django.db.models.FileField" title="django.db.models.FileField"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileField</span></code></a> and
<a class="reference internal" href="../../models/fields.html#django.db.models.ImageField" title="django.db.models.ImageField"><code class="xref py py-class docutils literal notranslate"><span class="pre">ImageField</span></code></a>).</p>
<p>It is possible to nest array fields - you can specify an instance of
<code class="docutils literal notranslate"><span class="pre">ArrayField</span></code> as the <code class="docutils literal notranslate"><span class="pre">base_field</span></code>. For example:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.contrib.postgres.fields</span><span class="w"> </span><span class="kn">import</span> <span class="n">ArrayField</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.db</span><span class="w"> </span><span class="kn">import</span> <span class="n">models</span>


<span class="k">class</span><span class="w"> </span><span class="nc">ChessBoard</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">board</span> <span class="o">=</span> <span class="n">ArrayField</span><span class="p">(</span>
        <span class="n">ArrayField</span><span class="p">(</span>
            <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span> <span class="n">blank</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span>
            <span class="n">size</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span>
        <span class="p">),</span>
        <span class="n">size</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span>
    <span class="p">)</span>
</pre></div>
</div>
<p>Transformation of values between the database and the model, validation
of data and configuration, and serialization are all delegated to the
underlying base field.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.ArrayField.size">
<span class="sig-name descname"><span class="pre">size</span></span><a class="headerlink" href="#django.contrib.postgres.fields.ArrayField.size" title="Link to this definition">¶</a></dt>
<dd><p>This is an optional argument.</p>
<p>If passed, the array will have a maximum size as specified. This will
be passed to the database, although PostgreSQL at present does not
enforce the restriction.</p>
</dd></dl>

</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When nesting <code class="docutils literal notranslate"><span class="pre">ArrayField</span></code>, whether you use the <code class="docutils literal notranslate"><span class="pre">size</span></code> parameter or not,
PostgreSQL requires that the arrays are rectangular:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.contrib.postgres.fields</span><span class="w"> </span><span class="kn">import</span> <span class="n">ArrayField</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.db</span><span class="w"> </span><span class="kn">import</span> <span class="n">models</span>


<span class="k">class</span><span class="w"> </span><span class="nc">Board</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">pieces</span> <span class="o">=</span> <span class="n">ArrayField</span><span class="p">(</span><span class="n">ArrayField</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">IntegerField</span><span class="p">()))</span>


<span class="c1"># Valid</span>
<span class="n">Board</span><span class="p">(</span>
    <span class="n">pieces</span><span class="o">=</span><span class="p">[</span>
        <span class="p">[</span><span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">2</span><span class="p">,</span> <span class="mi">1</span><span class="p">],</span>
    <span class="p">]</span>
<span class="p">)</span>

<span class="c1"># Not valid</span>
<span class="n">Board</span><span class="p">(</span>
    <span class="n">pieces</span><span class="o">=</span><span class="p">[</span>
        <span class="p">[</span><span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">],</span>
        <span class="p">[</span><span class="mi">2</span><span class="p">],</span>
    <span class="p">]</span>
<span class="p">)</span>
</pre></div>
</div>
<p>If irregular shapes are required, then the underlying field should be made
nullable and the values padded with <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
<section id="s-querying-arrayfield">
<span id="querying-arrayfield"></span><h3>Querying <code class="docutils literal notranslate"><span class="pre">ArrayField</span></code><a class="headerlink" href="#querying-arrayfield" title="Link to this heading">¶</a></h3>
<p>There are a number of custom lookups and transforms for <a class="reference internal" href="#django.contrib.postgres.fields.ArrayField" title="django.contrib.postgres.fields.ArrayField"><code class="xref py py-class docutils literal notranslate"><span class="pre">ArrayField</span></code></a>.
We will use the following example model:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.contrib.postgres.fields</span><span class="w"> </span><span class="kn">import</span> <span class="n">ArrayField</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.db</span><span class="w"> </span><span class="kn">import</span> <span class="n">models</span>


<span class="k">class</span><span class="w"> </span><span class="nc">Post</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">200</span><span class="p">)</span>
    <span class="n">tags</span> <span class="o">=</span> <span class="n">ArrayField</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">200</span><span class="p">),</span> <span class="n">blank</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__str__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
</pre></div>
</div>
<section id="s-contains">
<span id="s-std-fieldlookup-arrayfield.contains"></span><span id="contains"></span><span id="std-fieldlookup-arrayfield.contains"></span><h4><code class="docutils literal notranslate"><span class="pre">contains</span></code><a class="headerlink" href="#contains" title="Link to this heading">¶</a></h4>
<p>The <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-contains"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">contains</span></code></a> lookup is overridden on <a class="reference internal" href="#django.contrib.postgres.fields.ArrayField" title="django.contrib.postgres.fields.ArrayField"><code class="xref py py-class docutils literal notranslate"><span class="pre">ArrayField</span></code></a>. The
returned objects will be those where the values passed are a subset of the
data. It uses the SQL operator <code class="docutils literal notranslate"><span class="pre">&#64;&gt;</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;First post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Second post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Third post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;tutorial&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__contains</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__contains</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;django&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Third post&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__contains</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;django&quot;</span><span class="p">,</span> <span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-contained-by">
<span id="s-std-fieldlookup-arrayfield.contained_by"></span><span id="contained-by"></span><span id="std-fieldlookup-arrayfield.contained_by"></span><h4><code class="docutils literal notranslate"><span class="pre">contained_by</span></code><a class="headerlink" href="#contained-by" title="Link to this heading">¶</a></h4>
<p>This is the inverse of the <a class="reference internal" href="#std-fieldlookup-arrayfield.contains"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">contains</span></code></a> lookup -
the objects returned will be those where the data is a subset of the values
passed. It uses the SQL operator <code class="docutils literal notranslate"><span class="pre">&lt;&#64;</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;First post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Second post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Third post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;tutorial&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__contained_by</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__contained_by</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">,</span> <span class="s2">&quot;tutorial&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;, &lt;Post: Third post&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-overlap">
<span id="s-std-fieldlookup-arrayfield.overlap"></span><span id="overlap"></span><span id="std-fieldlookup-arrayfield.overlap"></span><h4><code class="docutils literal notranslate"><span class="pre">overlap</span></code><a class="headerlink" href="#overlap" title="Link to this heading">¶</a></h4>
<p>Returns objects where the data shares any results with the values passed. Uses
the SQL operator <code class="docutils literal notranslate"><span class="pre">&amp;&amp;</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;First post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Second post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;tutorial&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Third post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;tutorial&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__overlap</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__overlap</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;tutorial&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;, &lt;Post: Third post&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__overlap</span><span class="o">=</span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">values_list</span><span class="p">(</span><span class="s2">&quot;tags&quot;</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;, &lt;Post: Third post&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-len">
<span id="s-std-fieldlookup-arrayfield.len"></span><span id="len"></span><span id="std-fieldlookup-arrayfield.len"></span><h4><code class="docutils literal notranslate"><span class="pre">len</span></code><a class="headerlink" href="#len" title="Link to this heading">¶</a></h4>
<p>Returns the length of the array. The lookups available afterward are those
available for <a class="reference internal" href="../../models/fields.html#django.db.models.IntegerField" title="django.db.models.IntegerField"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntegerField</span></code></a>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;First post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Second post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__len</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Post: Second post&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-index-transforms">
<span id="s-std-fieldlookup-arrayfield.index"></span><span id="index-transforms"></span><span id="std-fieldlookup-arrayfield.index"></span><h4>Index transforms<a class="headerlink" href="#index-transforms" title="Link to this heading">¶</a></h4>
<p>Index transforms index into the array. Any non-negative integer can be used.
There are no errors if it exceeds the <a class="reference internal" href="#django.contrib.postgres.fields.ArrayField.size" title="django.contrib.postgres.fields.ArrayField.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">size</span></code></a> of the
array. The lookups available after the transform are those from the
<a class="reference internal" href="#django.contrib.postgres.fields.ArrayField.base_field" title="django.contrib.postgres.fields.ArrayField.base_field"><code class="xref py py-attr docutils literal notranslate"><span class="pre">base_field</span></code></a>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;First post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Second post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__0</span><span class="o">=</span><span class="s2">&quot;thoughts&quot;</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__1__iexact</span><span class="o">=</span><span class="s2">&quot;Django&quot;</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__276</span><span class="o">=</span><span class="s2">&quot;javascript&quot;</span><span class="p">)</span>
<span class="go">&lt;QuerySet []&gt;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>PostgreSQL uses 1-based indexing for array fields when writing raw SQL.
However these indexes and those used in <a class="reference internal" href="#std-fieldlookup-arrayfield.slice"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">slices</span></code></a>
use 0-based indexing to be consistent with Python.</p>
</div>
</section>
<section id="s-slice-transforms">
<span id="s-std-fieldlookup-arrayfield.slice"></span><span id="slice-transforms"></span><span id="std-fieldlookup-arrayfield.slice"></span><h4>Slice transforms<a class="headerlink" href="#slice-transforms" title="Link to this heading">¶</a></h4>
<p>Slice transforms take a slice of the array. Any two non-negative integers can
be used, separated by a single underscore. The lookups available after the
transform do not change. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;First post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">,</span> <span class="s2">&quot;django&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Second post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Third post&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;django&quot;</span><span class="p">,</span> <span class="s2">&quot;python&quot;</span><span class="p">,</span> <span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__0_1</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Post</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">tags__0_2__contains</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;thoughts&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Post: First post&gt;, &lt;Post: Second post&gt;]&gt;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>PostgreSQL uses 1-based indexing for array fields when writing raw SQL.
However these slices and those used in <a class="reference internal" href="#std-fieldlookup-arrayfield.index"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">indexes</span></code></a>
use 0-based indexing to be consistent with Python.</p>
</div>
<div class="admonition-multidimensional-arrays-with-indexes-and-slices admonition">
<p class="admonition-title">Multidimensional arrays with indexes and slices</p>
<p>PostgreSQL has some rather esoteric behavior when using indexes and slices
on multidimensional arrays. It will always work to use indexes to reach
down to the final underlying data, but most other slices behave strangely
at the database level and cannot be supported in a logical, consistent
fashion by Django.</p>
</div>
</section>
</section>
</section>
<section id="s-hstorefield">
<span id="hstorefield"></span><h2><code class="docutils literal notranslate"><span class="pre">HStoreField</span></code><a class="headerlink" href="#hstorefield" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.HStoreField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">HStoreField</span></span>(<em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.HStoreField" title="Link to this definition">¶</a></dt>
<dd><p>A field for storing key-value pairs. The Python data type used is a
<code class="docutils literal notranslate"><span class="pre">dict</span></code>. Keys must be strings, and values may be either strings or nulls
(<code class="docutils literal notranslate"><span class="pre">None</span></code> in Python).</p>
<p>To use this field, you’ll need to:</p>
<ol class="arabic simple">
<li><p>Add <code class="docutils literal notranslate"><span class="pre">'django.contrib.postgres'</span></code> in your <a class="reference internal" href="../../settings.html#std-setting-INSTALLED_APPS"><code class="xref std std-setting docutils literal notranslate"><span class="pre">INSTALLED_APPS</span></code></a>.</p></li>
<li><p><a class="reference internal" href="operations.html#create-postgresql-extensions"><span class="std std-ref">Set up the hstore extension</span></a> in
PostgreSQL.</p></li>
</ol>
<p>You’ll see an error like <code class="docutils literal notranslate"><span class="pre">can't</span> <span class="pre">adapt</span> <span class="pre">type</span> <span class="pre">'dict'</span></code> if you skip the first
step, or <code class="docutils literal notranslate"><span class="pre">type</span> <span class="pre">&quot;hstore&quot;</span> <span class="pre">does</span> <span class="pre">not</span> <span class="pre">exist</span></code> if you skip the second.</p>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On occasions it may be useful to require or restrict the keys which are
valid for a given field. This can be done using the
<a class="reference internal" href="validators.html#django.contrib.postgres.validators.KeysValidator" title="django.contrib.postgres.validators.KeysValidator"><code class="xref py py-class docutils literal notranslate"><span class="pre">KeysValidator</span></code></a>.</p>
</div>
<section id="s-querying-hstorefield">
<span id="querying-hstorefield"></span><h3>Querying <code class="docutils literal notranslate"><span class="pre">HStoreField</span></code><a class="headerlink" href="#querying-hstorefield" title="Link to this heading">¶</a></h3>
<p>In addition to the ability to query by key, there are a number of custom
lookups available for <code class="docutils literal notranslate"><span class="pre">HStoreField</span></code>.</p>
<p>We will use the following example model:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.contrib.postgres.fields</span><span class="w"> </span><span class="kn">import</span> <span class="n">HStoreField</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.db</span><span class="w"> </span><span class="kn">import</span> <span class="n">models</span>


<span class="k">class</span><span class="w"> </span><span class="nc">Dog</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">200</span><span class="p">)</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">HStoreField</span><span class="p">()</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__str__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
</pre></div>
</div>
<section id="s-key-lookups">
<span id="s-std-fieldlookup-hstorefield.key"></span><span id="key-lookups"></span><span id="std-fieldlookup-hstorefield.key"></span><h4>Key lookups<a class="headerlink" href="#key-lookups" title="Link to this heading">¶</a></h4>
<p>To query based on a given key, you can use that key as the lookup name:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Rufus&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;labrador&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Meg&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">})</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__breed</span><span class="o">=</span><span class="s2">&quot;collie&quot;</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Dog: Meg&gt;]&gt;</span>
</pre></div>
</div>
<p>You can chain other lookups after key lookups:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__breed__contains</span><span class="o">=</span><span class="s2">&quot;l&quot;</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Dog: Rufus&gt;, &lt;Dog: Meg&gt;]&gt;</span>
</pre></div>
</div>
<p>or use <code class="docutils literal notranslate"><span class="pre">F()</span></code> expressions to annotate a key value. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span><span class="w"> </span><span class="nn">django.db.models</span><span class="w"> </span><span class="kn">import</span> <span class="n">F</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">rufus</span> <span class="o">=</span> <span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">annotate</span><span class="p">(</span><span class="n">breed</span><span class="o">=</span><span class="n">F</span><span class="p">(</span><span class="s2">&quot;data__breed&quot;</span><span class="p">))[</span><span class="mi">0</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">rufus</span><span class="o">.</span><span class="n">breed</span>
<span class="go">&#39;labrador&#39;</span>
</pre></div>
</div>
<p>If the key you wish to query by clashes with the name of another lookup, you
need to use the <a class="reference internal" href="#std-fieldlookup-hstorefield.contains"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">hstorefield.contains</span></code></a> lookup instead.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Key transforms can also be chained with: <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-contains"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">contains</span></code></a>,
<a class="reference internal" href="../../models/querysets.html#std-fieldlookup-icontains"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">icontains</span></code></a>, <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-endswith"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">endswith</span></code></a>, <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-iendswith"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">iendswith</span></code></a>,
<a class="reference internal" href="../../models/querysets.html#std-fieldlookup-iexact"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">iexact</span></code></a>, <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-regex"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">regex</span></code></a>, <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-iregex"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">iregex</span></code></a>, <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-startswith"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">startswith</span></code></a>,
and <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-istartswith"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">istartswith</span></code></a> lookups.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Since any string could be a key in a hstore value, any lookup other than
those listed below will be interpreted as a key lookup. No errors are
raised. Be extra careful for typing mistakes, and always check your queries
work as you intend.</p>
</div>
</section>
<section id="s-std-fieldlookup-hstorefield.contains">
<span id="s-id1"></span><span id="std-fieldlookup-hstorefield.contains"></span><span id="id1"></span><h4><code class="docutils literal notranslate"><span class="pre">contains</span></code><a class="headerlink" href="#std-fieldlookup-hstorefield.contains" title="Link to this heading">¶</a></h4>
<p>The <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-contains"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">contains</span></code></a> lookup is overridden on
<a class="reference internal" href="#django.contrib.postgres.fields.HStoreField" title="django.contrib.postgres.fields.HStoreField"><code class="xref py py-class docutils literal notranslate"><span class="pre">HStoreField</span></code></a>. The returned objects are
those where the given <code class="docutils literal notranslate"><span class="pre">dict</span></code> of key-value pairs are all contained in the
field. It uses the SQL operator <code class="docutils literal notranslate"><span class="pre">&#64;&gt;</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Rufus&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;labrador&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Meg&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Fred&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{})</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__contains</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>
<span class="go">&lt;QuerySet [&lt;Dog: Rufus&gt;, &lt;Dog: Meg&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__contains</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">})</span>
<span class="go">&lt;QuerySet [&lt;Dog: Meg&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-std-fieldlookup-hstorefield.contained_by">
<span id="s-id2"></span><span id="std-fieldlookup-hstorefield.contained_by"></span><span id="id2"></span><h4><code class="docutils literal notranslate"><span class="pre">contained_by</span></code><a class="headerlink" href="#std-fieldlookup-hstorefield.contained_by" title="Link to this heading">¶</a></h4>
<p>This is the inverse of the <a class="reference internal" href="#std-fieldlookup-hstorefield.contains"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">contains</span></code></a> lookup -
the objects returned will be those where the key-value pairs on the object are
a subset of those in the value passed. It uses the SQL operator <code class="docutils literal notranslate"><span class="pre">&lt;&#64;</span></code>. For
example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Rufus&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;labrador&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Meg&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Fred&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{})</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__contained_by</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>
<span class="go">&lt;QuerySet [&lt;Dog: Meg&gt;, &lt;Dog: Fred&gt;]&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__contained_by</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">})</span>
<span class="go">&lt;QuerySet [&lt;Dog: Fred&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-has-key">
<span id="s-std-fieldlookup-hstorefield.has_key"></span><span id="has-key"></span><span id="std-fieldlookup-hstorefield.has_key"></span><h4><code class="docutils literal notranslate"><span class="pre">has_key</span></code><a class="headerlink" href="#has-key" title="Link to this heading">¶</a></h4>
<p>Returns objects where the given key is in the data. Uses the SQL operator
<code class="docutils literal notranslate"><span class="pre">?</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Rufus&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;labrador&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Meg&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__has_key</span><span class="o">=</span><span class="s2">&quot;owner&quot;</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Dog: Meg&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-has-any-keys">
<span id="s-std-fieldlookup-hstorefield.has_any_keys"></span><span id="has-any-keys"></span><span id="std-fieldlookup-hstorefield.has_any_keys"></span><h4><code class="docutils literal notranslate"><span class="pre">has_any_keys</span></code><a class="headerlink" href="#has-any-keys" title="Link to this heading">¶</a></h4>
<p>Returns objects where any of the given keys are in the data. Uses the SQL
operator <code class="docutils literal notranslate"><span class="pre">?|</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Rufus&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;labrador&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Meg&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Fred&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{})</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__has_any_keys</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;owner&quot;</span><span class="p">,</span> <span class="s2">&quot;breed&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Dog: Rufus&gt;, &lt;Dog: Meg&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-has-keys">
<span id="s-std-fieldlookup-hstorefield.has_keys"></span><span id="has-keys"></span><span id="std-fieldlookup-hstorefield.has_keys"></span><h4><code class="docutils literal notranslate"><span class="pre">has_keys</span></code><a class="headerlink" href="#has-keys" title="Link to this heading">¶</a></h4>
<p>Returns objects where all of the given keys are in the data. Uses the SQL operator
<code class="docutils literal notranslate"><span class="pre">?&amp;</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Rufus&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Meg&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__has_keys</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;breed&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Dog: Meg&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-keys">
<span id="s-std-fieldlookup-hstorefield.keys"></span><span id="keys"></span><span id="std-fieldlookup-hstorefield.keys"></span><h4><code class="docutils literal notranslate"><span class="pre">keys</span></code><a class="headerlink" href="#keys" title="Link to this heading">¶</a></h4>
<p>Returns objects where the array of keys is the given value. Note that the order
is not guaranteed to be reliable, so this transform is mainly useful for using
in conjunction with lookups on
<a class="reference internal" href="#django.contrib.postgres.fields.ArrayField" title="django.contrib.postgres.fields.ArrayField"><code class="xref py py-class docutils literal notranslate"><span class="pre">ArrayField</span></code></a>. Uses the SQL function
<code class="docutils literal notranslate"><span class="pre">akeys()</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Rufus&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;toy&quot;</span><span class="p">:</span> <span class="s2">&quot;bone&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Meg&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__keys__overlap</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;breed&quot;</span><span class="p">,</span> <span class="s2">&quot;toy&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Dog: Rufus&gt;, &lt;Dog: Meg&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-values">
<span id="s-std-fieldlookup-hstorefield.values"></span><span id="values"></span><span id="std-fieldlookup-hstorefield.values"></span><h4><code class="docutils literal notranslate"><span class="pre">values</span></code><a class="headerlink" href="#values" title="Link to this heading">¶</a></h4>
<p>Returns objects where the array of values is the given value. Note that the
order is not guaranteed to be reliable, so this transform is mainly useful for
using in conjunction with lookups on
<a class="reference internal" href="#django.contrib.postgres.fields.ArrayField" title="django.contrib.postgres.fields.ArrayField"><code class="xref py py-class docutils literal notranslate"><span class="pre">ArrayField</span></code></a>. Uses the SQL function
<code class="docutils literal notranslate"><span class="pre">avals()</span></code>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Rufus&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;labrador&quot;</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Meg&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;breed&quot;</span><span class="p">:</span> <span class="s2">&quot;collie&quot;</span><span class="p">,</span> <span class="s2">&quot;owner&quot;</span><span class="p">:</span> <span class="s2">&quot;Bob&quot;</span><span class="p">})</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Dog</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">data__values__contains</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;collie&quot;</span><span class="p">])</span>
<span class="go">&lt;QuerySet [&lt;Dog: Meg&gt;]&gt;</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="s-range-fields">
<span id="s-id3"></span><span id="range-fields"></span><span id="id3"></span><h2>Range Fields<a class="headerlink" href="#range-fields" title="Link to this heading">¶</a></h2>
<p>There are five range field types, corresponding to the built-in range types in
PostgreSQL. These fields are used to store a range of values; for example the
start and end timestamps of an event, or the range of ages an activity is
suitable for.</p>
<p>All of the range fields translate to <a class="reference external" href="https://www.psycopg.org/psycopg3/docs/basic/pgtypes.html#adapt-range" title="(in psycopg)"><span class="xref std std-ref">psycopg Range objects</span></a> in Python, but also accept tuples as input if no bounds
information is necessary. The default is lower bound included, upper bound
excluded, that is <code class="docutils literal notranslate"><span class="pre">[)</span></code> (see the PostgreSQL documentation for details about
<a class="reference external" href="https://www.postgresql.org/docs/current/rangetypes.html#RANGETYPES-IO">different bounds</a>). The default bounds can be changed for non-discrete range
fields (<a class="reference internal" href="#django.contrib.postgres.fields.DateTimeRangeField" title="django.contrib.postgres.fields.DateTimeRangeField"><code class="xref py py-class docutils literal notranslate"><span class="pre">DateTimeRangeField</span></code></a> and <a class="reference internal" href="#django.contrib.postgres.fields.DecimalRangeField" title="django.contrib.postgres.fields.DecimalRangeField"><code class="xref py py-class docutils literal notranslate"><span class="pre">DecimalRangeField</span></code></a>) by using
the <code class="docutils literal notranslate"><span class="pre">default_bounds</span></code> argument.</p>
<div class="admonition-postgresql-normalizes-a-range-with-no-points-to-the-empty-range admonition">
<p class="admonition-title">PostgreSQL normalizes a range with no points to the empty range</p>
<p>A range with equal values specified for an included lower bound and an
excluded upper bound, such as <code class="docutils literal notranslate"><span class="pre">Range(datetime.date(2005,</span> <span class="pre">6,</span> <span class="pre">21),</span>
<span class="pre">datetime.date(2005,</span> <span class="pre">6,</span> <span class="pre">21))</span></code> or <code class="docutils literal notranslate"><span class="pre">[4,</span> <span class="pre">4)</span></code>, has no points. PostgreSQL will
normalize the value to empty when saving to the database, and the original
bound values will be lost. See the <a class="reference external" href="https://www.postgresql.org/docs/current/rangetypes.html#RANGETYPES-IO">PostgreSQL documentation for details</a>.</p>
</div>
<section id="s-integerrangefield">
<span id="integerrangefield"></span><h3><code class="docutils literal notranslate"><span class="pre">IntegerRangeField</span></code><a class="headerlink" href="#integerrangefield" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.IntegerRangeField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">IntegerRangeField</span></span>(<em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.IntegerRangeField" title="Link to this definition">¶</a></dt>
<dd><p>Stores a range of integers. Based on an
<a class="reference internal" href="../../models/fields.html#django.db.models.IntegerField" title="django.db.models.IntegerField"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntegerField</span></code></a>. Represented by an <code class="docutils literal notranslate"><span class="pre">int4range</span></code> in
the database and a
<code class="docutils literal notranslate"><span class="pre">django.db.backends.postgresql.psycopg_any.NumericRange</span></code> in Python.</p>
<p>Regardless of the bounds specified when saving the data, PostgreSQL always
returns a range in a canonical form that includes the lower bound and
excludes the upper bound, that is <code class="docutils literal notranslate"><span class="pre">[)</span></code>.</p>
</dd></dl>

</section>
<section id="s-bigintegerrangefield">
<span id="bigintegerrangefield"></span><h3><code class="docutils literal notranslate"><span class="pre">BigIntegerRangeField</span></code><a class="headerlink" href="#bigintegerrangefield" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.BigIntegerRangeField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">BigIntegerRangeField</span></span>(<em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.BigIntegerRangeField" title="Link to this definition">¶</a></dt>
<dd><p>Stores a range of large integers. Based on a
<a class="reference internal" href="../../models/fields.html#django.db.models.BigIntegerField" title="django.db.models.BigIntegerField"><code class="xref py py-class docutils literal notranslate"><span class="pre">BigIntegerField</span></code></a>. Represented by an <code class="docutils literal notranslate"><span class="pre">int8range</span></code>
in the database and a
<code class="docutils literal notranslate"><span class="pre">django.db.backends.postgresql.psycopg_any.NumericRange</span></code> in Python.</p>
<p>Regardless of the bounds specified when saving the data, PostgreSQL always
returns a range in a canonical form that includes the lower bound and
excludes the upper bound, that is <code class="docutils literal notranslate"><span class="pre">[)</span></code>.</p>
</dd></dl>

</section>
<section id="s-decimalrangefield">
<span id="decimalrangefield"></span><h3><code class="docutils literal notranslate"><span class="pre">DecimalRangeField</span></code><a class="headerlink" href="#decimalrangefield" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.DecimalRangeField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DecimalRangeField</span></span>(<em class="sig-param"><span class="n"><span class="pre">default_bounds</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'[)'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.DecimalRangeField" title="Link to this definition">¶</a></dt>
<dd><p>Stores a range of floating point values. Based on a
<a class="reference internal" href="../../models/fields.html#django.db.models.DecimalField" title="django.db.models.DecimalField"><code class="xref py py-class docutils literal notranslate"><span class="pre">DecimalField</span></code></a>. Represented by a <code class="docutils literal notranslate"><span class="pre">numrange</span></code> in
the database and a
<code class="docutils literal notranslate"><span class="pre">django.db.backends.postgresql.psycopg_any.NumericRange</span></code> in Python.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.DecimalRangeField.default_bounds">
<span class="sig-name descname"><span class="pre">default_bounds</span></span><a class="headerlink" href="#django.contrib.postgres.fields.DecimalRangeField.default_bounds" title="Link to this definition">¶</a></dt>
<dd><p>Optional. The value of <code class="docutils literal notranslate"><span class="pre">bounds</span></code> for list and tuple inputs. The
default is lower bound included, upper bound excluded, that is <code class="docutils literal notranslate"><span class="pre">[)</span></code>
(see the PostgreSQL documentation for details about
<a class="reference external" href="https://www.postgresql.org/docs/current/rangetypes.html#RANGETYPES-IO">different bounds</a>). <code class="docutils literal notranslate"><span class="pre">default_bounds</span></code> is not used for
<code class="docutils literal notranslate"><span class="pre">django.db.backends.postgresql.psycopg_any.NumericRange</span></code> inputs.</p>
</dd></dl>

</dd></dl>

</section>
<section id="s-datetimerangefield">
<span id="datetimerangefield"></span><h3><code class="docutils literal notranslate"><span class="pre">DateTimeRangeField</span></code><a class="headerlink" href="#datetimerangefield" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.DateTimeRangeField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DateTimeRangeField</span></span>(<em class="sig-param"><span class="n"><span class="pre">default_bounds</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'[)'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.DateTimeRangeField" title="Link to this definition">¶</a></dt>
<dd><p>Stores a range of timestamps. Based on a
<a class="reference internal" href="../../models/fields.html#django.db.models.DateTimeField" title="django.db.models.DateTimeField"><code class="xref py py-class docutils literal notranslate"><span class="pre">DateTimeField</span></code></a>. Represented by a <code class="docutils literal notranslate"><span class="pre">tstzrange</span></code> in
the database and a
<code class="docutils literal notranslate"><span class="pre">django.db.backends.postgresql.psycopg_any.DateTimeTZRange</span></code> in Python.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.DateTimeRangeField.default_bounds">
<span class="sig-name descname"><span class="pre">default_bounds</span></span><a class="headerlink" href="#django.contrib.postgres.fields.DateTimeRangeField.default_bounds" title="Link to this definition">¶</a></dt>
<dd><p>Optional. The value of <code class="docutils literal notranslate"><span class="pre">bounds</span></code> for list and tuple inputs. The
default is lower bound included, upper bound excluded, that is <code class="docutils literal notranslate"><span class="pre">[)</span></code>
(see the PostgreSQL documentation for details about
<a class="reference external" href="https://www.postgresql.org/docs/current/rangetypes.html#RANGETYPES-IO">different bounds</a>). <code class="docutils literal notranslate"><span class="pre">default_bounds</span></code> is not used for
<code class="docutils literal notranslate"><span class="pre">django.db.backends.postgresql.psycopg_any.DateTimeTZRange</span></code> inputs.</p>
</dd></dl>

</dd></dl>

</section>
<section id="s-daterangefield">
<span id="daterangefield"></span><h3><code class="docutils literal notranslate"><span class="pre">DateRangeField</span></code><a class="headerlink" href="#daterangefield" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.DateRangeField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">DateRangeField</span></span>(<em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.DateRangeField" title="Link to this definition">¶</a></dt>
<dd><p>Stores a range of dates. Based on a
<a class="reference internal" href="../../models/fields.html#django.db.models.DateField" title="django.db.models.DateField"><code class="xref py py-class docutils literal notranslate"><span class="pre">DateField</span></code></a>. Represented by a <code class="docutils literal notranslate"><span class="pre">daterange</span></code> in the
database and a <code class="docutils literal notranslate"><span class="pre">django.db.backends.postgresql.psycopg_any.DateRange</span></code> in
Python.</p>
<p>Regardless of the bounds specified when saving the data, PostgreSQL always
returns a range in a canonical form that includes the lower bound and
excludes the upper bound, that is <code class="docutils literal notranslate"><span class="pre">[)</span></code>.</p>
</dd></dl>

</section>
<section id="s-querying-range-fields">
<span id="querying-range-fields"></span><h3>Querying Range Fields<a class="headerlink" href="#querying-range-fields" title="Link to this heading">¶</a></h3>
<p>There are a number of custom lookups and transforms for range fields. They are
available on all the above fields, but we will use the following example
model:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.contrib.postgres.fields</span><span class="w"> </span><span class="kn">import</span> <span class="n">IntegerRangeField</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.db</span><span class="w"> </span><span class="kn">import</span> <span class="n">models</span>


<span class="k">class</span><span class="w"> </span><span class="nc">Event</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">200</span><span class="p">)</span>
    <span class="n">ages</span> <span class="o">=</span> <span class="n">IntegerRangeField</span><span class="p">()</span>
    <span class="n">start</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">DateTimeField</span><span class="p">()</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__str__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
</pre></div>
</div>
<p>We will also use the following example objects:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span><span class="w"> </span><span class="nn">datetime</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span><span class="w"> </span><span class="nn">django.utils</span><span class="w"> </span><span class="kn">import</span> <span class="n">timezone</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">now</span> <span class="o">=</span> <span class="n">timezone</span><span class="o">.</span><span class="n">now</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;Soft play&quot;</span><span class="p">,</span> <span class="n">ages</span><span class="o">=</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span> <span class="n">start</span><span class="o">=</span><span class="n">now</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">create</span><span class="p">(</span>
<span class="gp">... </span>    <span class="n">name</span><span class="o">=</span><span class="s2">&quot;Pub trip&quot;</span><span class="p">,</span> <span class="n">ages</span><span class="o">=</span><span class="p">(</span><span class="mi">21</span><span class="p">,</span> <span class="kc">None</span><span class="p">),</span> <span class="n">start</span><span class="o">=</span><span class="n">now</span> <span class="o">-</span> <span class="n">datetime</span><span class="o">.</span><span class="n">timedelta</span><span class="p">(</span><span class="n">days</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="gp">... </span><span class="p">)</span>
</pre></div>
</div>
<p>and <code class="docutils literal notranslate"><span class="pre">NumericRange</span></code>:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span><span class="w"> </span><span class="nn">django.db.backends.postgresql.psycopg_any</span><span class="w"> </span><span class="kn">import</span> <span class="n">NumericRange</span>
</pre></div>
</div>
<section id="s-containment-functions">
<span id="containment-functions"></span><h4>Containment functions<a class="headerlink" href="#containment-functions" title="Link to this heading">¶</a></h4>
<p>As with other PostgreSQL fields, there are three standard containment
operators: <code class="docutils literal notranslate"><span class="pre">contains</span></code>, <code class="docutils literal notranslate"><span class="pre">contained_by</span></code> and <code class="docutils literal notranslate"><span class="pre">overlap</span></code>, using the SQL
operators <code class="docutils literal notranslate"><span class="pre">&#64;&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;&#64;</span></code>, and <code class="docutils literal notranslate"><span class="pre">&amp;&amp;</span></code> respectively.</p>
<section id="s-std-fieldlookup-rangefield.contains">
<span id="s-id4"></span><span id="std-fieldlookup-rangefield.contains"></span><span id="id4"></span><h5><code class="docutils literal notranslate"><span class="pre">contains</span></code><a class="headerlink" href="#std-fieldlookup-rangefield.contains" title="Link to this heading">¶</a></h5>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__contains</span><span class="o">=</span><span class="n">NumericRange</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-std-fieldlookup-rangefield.contained_by">
<span id="s-id5"></span><span id="std-fieldlookup-rangefield.contained_by"></span><span id="id5"></span><h5><code class="docutils literal notranslate"><span class="pre">contained_by</span></code><a class="headerlink" href="#std-fieldlookup-rangefield.contained_by" title="Link to this heading">¶</a></h5>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__contained_by</span><span class="o">=</span><span class="n">NumericRange</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">15</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;]&gt;</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">contained_by</span></code> lookup is also available on the non-range field types:
<a class="reference internal" href="../../models/fields.html#django.db.models.SmallAutoField" title="django.db.models.SmallAutoField"><code class="xref py py-class docutils literal notranslate"><span class="pre">SmallAutoField</span></code></a>,
<a class="reference internal" href="../../models/fields.html#django.db.models.AutoField" title="django.db.models.AutoField"><code class="xref py py-class docutils literal notranslate"><span class="pre">AutoField</span></code></a>, <a class="reference internal" href="../../models/fields.html#django.db.models.BigAutoField" title="django.db.models.BigAutoField"><code class="xref py py-class docutils literal notranslate"><span class="pre">BigAutoField</span></code></a>,
<a class="reference internal" href="../../models/fields.html#django.db.models.SmallIntegerField" title="django.db.models.SmallIntegerField"><code class="xref py py-class docutils literal notranslate"><span class="pre">SmallIntegerField</span></code></a>,
<a class="reference internal" href="../../models/fields.html#django.db.models.IntegerField" title="django.db.models.IntegerField"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntegerField</span></code></a>,
<a class="reference internal" href="../../models/fields.html#django.db.models.BigIntegerField" title="django.db.models.BigIntegerField"><code class="xref py py-class docutils literal notranslate"><span class="pre">BigIntegerField</span></code></a>,
<a class="reference internal" href="../../models/fields.html#django.db.models.DecimalField" title="django.db.models.DecimalField"><code class="xref py py-class docutils literal notranslate"><span class="pre">DecimalField</span></code></a>, <a class="reference internal" href="../../models/fields.html#django.db.models.FloatField" title="django.db.models.FloatField"><code class="xref py py-class docutils literal notranslate"><span class="pre">FloatField</span></code></a>,
<a class="reference internal" href="../../models/fields.html#django.db.models.DateField" title="django.db.models.DateField"><code class="xref py py-class docutils literal notranslate"><span class="pre">DateField</span></code></a>, and
<a class="reference internal" href="../../models/fields.html#django.db.models.DateTimeField" title="django.db.models.DateTimeField"><code class="xref py py-class docutils literal notranslate"><span class="pre">DateTimeField</span></code></a>. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span><span class="w"> </span><span class="nn">django.db.backends.postgresql.psycopg_any</span><span class="w"> </span><span class="kn">import</span> <span class="n">DateTimeTZRange</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
<span class="gp">... </span>    <span class="n">start__contained_by</span><span class="o">=</span><span class="n">DateTimeTZRange</span><span class="p">(</span>
<span class="gp">... </span>        <span class="n">timezone</span><span class="o">.</span><span class="n">now</span><span class="p">()</span> <span class="o">-</span> <span class="n">datetime</span><span class="o">.</span><span class="n">timedelta</span><span class="p">(</span><span class="n">hours</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
<span class="gp">... </span>        <span class="n">timezone</span><span class="o">.</span><span class="n">now</span><span class="p">()</span> <span class="o">+</span> <span class="n">datetime</span><span class="o">.</span><span class="n">timedelta</span><span class="p">(</span><span class="n">hours</span><span class="o">=</span><span class="mi">1</span><span class="p">),</span>
<span class="gp">... </span>    <span class="p">),</span>
<span class="gp">... </span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-std-fieldlookup-rangefield.overlap">
<span id="s-id6"></span><span id="std-fieldlookup-rangefield.overlap"></span><span id="id6"></span><h5><code class="docutils literal notranslate"><span class="pre">overlap</span></code><a class="headerlink" href="#std-fieldlookup-rangefield.overlap" title="Link to this heading">¶</a></h5>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__overlap</span><span class="o">=</span><span class="n">NumericRange</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">12</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;]&gt;</span>
</pre></div>
</div>
</section>
</section>
<section id="s-comparison-functions">
<span id="comparison-functions"></span><h4>Comparison functions<a class="headerlink" href="#comparison-functions" title="Link to this heading">¶</a></h4>
<p>Range fields support the standard lookups: <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-lt"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">lt</span></code></a>, <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-gt"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">gt</span></code></a>,
<a class="reference internal" href="../../models/querysets.html#std-fieldlookup-lte"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">lte</span></code></a> and <a class="reference internal" href="../../models/querysets.html#std-fieldlookup-gte"><code class="xref std std-lookup docutils literal notranslate"><span class="pre">gte</span></code></a>. These are not particularly helpful - they
compare the lower bounds first and then the upper bounds only if necessary.
This is also the strategy used to order by a range field. It is better to use
the specific range comparison operators.</p>
<section id="s-fully-lt">
<span id="s-std-fieldlookup-rangefield.fully_lt"></span><span id="fully-lt"></span><span id="std-fieldlookup-rangefield.fully_lt"></span><h5><code class="docutils literal notranslate"><span class="pre">fully_lt</span></code><a class="headerlink" href="#fully-lt" title="Link to this heading">¶</a></h5>
<p>The returned ranges are strictly less than the passed range. In other words,
all the points in the returned range are less than all those in the passed
range.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__fully_lt</span><span class="o">=</span><span class="n">NumericRange</span><span class="p">(</span><span class="mi">11</span><span class="p">,</span> <span class="mi">15</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-fully-gt">
<span id="s-std-fieldlookup-rangefield.fully_gt"></span><span id="fully-gt"></span><span id="std-fieldlookup-rangefield.fully_gt"></span><h5><code class="docutils literal notranslate"><span class="pre">fully_gt</span></code><a class="headerlink" href="#fully-gt" title="Link to this heading">¶</a></h5>
<p>The returned ranges are strictly greater than the passed range. In other words,
the all the points in the returned range are greater than all those in the
passed range.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__fully_gt</span><span class="o">=</span><span class="n">NumericRange</span><span class="p">(</span><span class="mi">11</span><span class="p">,</span> <span class="mi">15</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Event: Pub trip&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-not-lt">
<span id="s-std-fieldlookup-rangefield.not_lt"></span><span id="not-lt"></span><span id="std-fieldlookup-rangefield.not_lt"></span><h5><code class="docutils literal notranslate"><span class="pre">not_lt</span></code><a class="headerlink" href="#not-lt" title="Link to this heading">¶</a></h5>
<p>The returned ranges do not contain any points less than the passed range, that
is the lower bound of the returned range is at least the lower bound of the
passed range.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__not_lt</span><span class="o">=</span><span class="n">NumericRange</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">15</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;, &lt;Event: Pub trip&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-not-gt">
<span id="s-std-fieldlookup-rangefield.not_gt"></span><span id="not-gt"></span><span id="std-fieldlookup-rangefield.not_gt"></span><h5><code class="docutils literal notranslate"><span class="pre">not_gt</span></code><a class="headerlink" href="#not-gt" title="Link to this heading">¶</a></h5>
<p>The returned ranges do not contain any points greater than the passed range, that
is the upper bound of the returned range is at most the upper bound of the
passed range.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__not_gt</span><span class="o">=</span><span class="n">NumericRange</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">10</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-adjacent-to">
<span id="s-std-fieldlookup-rangefield.adjacent_to"></span><span id="adjacent-to"></span><span id="std-fieldlookup-rangefield.adjacent_to"></span><h5><code class="docutils literal notranslate"><span class="pre">adjacent_to</span></code><a class="headerlink" href="#adjacent-to" title="Link to this heading">¶</a></h5>
<p>The returned ranges share a bound with the passed range.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__adjacent_to</span><span class="o">=</span><span class="n">NumericRange</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">21</span><span class="p">))</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;, &lt;Event: Pub trip&gt;]&gt;</span>
</pre></div>
</div>
</section>
</section>
<section id="s-querying-using-the-bounds">
<span id="querying-using-the-bounds"></span><h4>Querying using the bounds<a class="headerlink" href="#querying-using-the-bounds" title="Link to this heading">¶</a></h4>
<p>Range fields support several extra lookups.</p>
<section id="s-startswith">
<span id="s-std-fieldlookup-rangefield.startswith"></span><span id="startswith"></span><span id="std-fieldlookup-rangefield.startswith"></span><h5><code class="docutils literal notranslate"><span class="pre">startswith</span></code><a class="headerlink" href="#startswith" title="Link to this heading">¶</a></h5>
<p>Returned objects have the given lower bound. Can be chained to valid lookups
for the base field.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__startswith</span><span class="o">=</span><span class="mi">21</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Event: Pub trip&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-endswith">
<span id="s-std-fieldlookup-rangefield.endswith"></span><span id="endswith"></span><span id="std-fieldlookup-rangefield.endswith"></span><h5><code class="docutils literal notranslate"><span class="pre">endswith</span></code><a class="headerlink" href="#endswith" title="Link to this heading">¶</a></h5>
<p>Returned objects have the given upper bound. Can be chained to valid lookups
for the base field.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__endswith</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-isempty">
<span id="s-std-fieldlookup-rangefield.isempty"></span><span id="isempty"></span><span id="std-fieldlookup-rangefield.isempty"></span><h5><code class="docutils literal notranslate"><span class="pre">isempty</span></code><a class="headerlink" href="#isempty" title="Link to this heading">¶</a></h5>
<p>Returned objects are empty ranges. Can be chained to valid lookups for a
<a class="reference internal" href="../../models/fields.html#django.db.models.BooleanField" title="django.db.models.BooleanField"><code class="xref py py-class docutils literal notranslate"><span class="pre">BooleanField</span></code></a>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__isempty</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">&lt;QuerySet []&gt;</span>
</pre></div>
</div>
</section>
<section id="s-lower-inc">
<span id="s-std-fieldlookup-rangefield.lower_inc"></span><span id="lower-inc"></span><span id="std-fieldlookup-rangefield.lower_inc"></span><h5><code class="docutils literal notranslate"><span class="pre">lower_inc</span></code><a class="headerlink" href="#lower-inc" title="Link to this heading">¶</a></h5>
<p>Returns objects that have inclusive or exclusive lower bounds, depending on the
boolean value passed. Can be chained to valid lookups for a
<a class="reference internal" href="../../models/fields.html#django.db.models.BooleanField" title="django.db.models.BooleanField"><code class="xref py py-class docutils literal notranslate"><span class="pre">BooleanField</span></code></a>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__lower_inc</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Event: Soft play&gt;, &lt;Event: Pub trip&gt;]&gt;</span>
</pre></div>
</div>
</section>
<section id="s-lower-inf">
<span id="s-std-fieldlookup-rangefield.lower_inf"></span><span id="lower-inf"></span><span id="std-fieldlookup-rangefield.lower_inf"></span><h5><code class="docutils literal notranslate"><span class="pre">lower_inf</span></code><a class="headerlink" href="#lower-inf" title="Link to this heading">¶</a></h5>
<p>Returns objects that have unbounded (infinite) or bounded lower bound,
depending on the boolean value passed. Can be chained to valid lookups for a
<a class="reference internal" href="../../models/fields.html#django.db.models.BooleanField" title="django.db.models.BooleanField"><code class="xref py py-class docutils literal notranslate"><span class="pre">BooleanField</span></code></a>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__lower_inf</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">&lt;QuerySet []&gt;</span>
</pre></div>
</div>
</section>
<section id="s-upper-inc">
<span id="s-std-fieldlookup-rangefield.upper_inc"></span><span id="upper-inc"></span><span id="std-fieldlookup-rangefield.upper_inc"></span><h5><code class="docutils literal notranslate"><span class="pre">upper_inc</span></code><a class="headerlink" href="#upper-inc" title="Link to this heading">¶</a></h5>
<p>Returns objects that have inclusive or exclusive upper bounds, depending on the
boolean value passed. Can be chained to valid lookups for a
<a class="reference internal" href="../../models/fields.html#django.db.models.BooleanField" title="django.db.models.BooleanField"><code class="xref py py-class docutils literal notranslate"><span class="pre">BooleanField</span></code></a>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__upper_inc</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">&lt;QuerySet []&gt;</span>
</pre></div>
</div>
</section>
<section id="s-upper-inf">
<span id="s-std-fieldlookup-rangefield.upper_inf"></span><span id="upper-inf"></span><span id="std-fieldlookup-rangefield.upper_inf"></span><h5><code class="docutils literal notranslate"><span class="pre">upper_inf</span></code><a class="headerlink" href="#upper-inf" title="Link to this heading">¶</a></h5>
<p>Returns objects that have unbounded (infinite) or bounded upper bound,
depending on the boolean value passed. Can be chained to valid lookups for a
<a class="reference internal" href="../../models/fields.html#django.db.models.BooleanField" title="django.db.models.BooleanField"><code class="xref py py-class docutils literal notranslate"><span class="pre">BooleanField</span></code></a>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Event</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">ages__upper_inf</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">&lt;QuerySet [&lt;Event: Pub trip&gt;]&gt;</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="s-defining-your-own-range-types">
<span id="defining-your-own-range-types"></span><h3>Defining your own range types<a class="headerlink" href="#defining-your-own-range-types" title="Link to this heading">¶</a></h3>
<p>PostgreSQL allows the definition of custom range types. Django’s model and form
field implementations use base classes below, and <code class="docutils literal notranslate"><span class="pre">psycopg</span></code> provides a
<a class="reference external" href="https://www.psycopg.org/psycopg3/docs/basic/pgtypes.html#psycopg.types.range.register_range" title="(in psycopg)"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_range()</span></code></a> to allow use of custom
range types.</p>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.RangeField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RangeField</span></span>(<em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.RangeField" title="Link to this definition">¶</a></dt>
<dd><p>Base class for model range fields.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.RangeField.base_field">
<span class="sig-name descname"><span class="pre">base_field</span></span><a class="headerlink" href="#django.contrib.postgres.fields.RangeField.base_field" title="Link to this definition">¶</a></dt>
<dd><p>The model field class to use.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.RangeField.range_type">
<span class="sig-name descname"><span class="pre">range_type</span></span><a class="headerlink" href="#django.contrib.postgres.fields.RangeField.range_type" title="Link to this definition">¶</a></dt>
<dd><p>The range type to use.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.RangeField.form_field">
<span class="sig-name descname"><span class="pre">form_field</span></span><a class="headerlink" href="#django.contrib.postgres.fields.RangeField.form_field" title="Link to this definition">¶</a></dt>
<dd><p>The form field class to use. Should be a subclass of
<a class="reference internal" href="#django.contrib.postgres.fields.django.contrib.postgres.forms.BaseRangeField" title="django.contrib.postgres.fields.django.contrib.postgres.forms.BaseRangeField"><code class="xref py py-class docutils literal notranslate"><span class="pre">django.contrib.postgres.forms.BaseRangeField</span></code></a>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.django.contrib.postgres.forms.BaseRangeField">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">django.contrib.postgres.forms.</span></span><span class="sig-name descname"><span class="pre">BaseRangeField</span></span><a class="headerlink" href="#django.contrib.postgres.fields.django.contrib.postgres.forms.BaseRangeField" title="Link to this definition">¶</a></dt>
<dd><p>Base class for form range fields.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.django.contrib.postgres.forms.BaseRangeField.base_field">
<span class="sig-name descname"><span class="pre">base_field</span></span><a class="headerlink" href="#django.contrib.postgres.fields.django.contrib.postgres.forms.BaseRangeField.base_field" title="Link to this definition">¶</a></dt>
<dd><p>The form field to use.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.django.contrib.postgres.forms.BaseRangeField.range_type">
<span class="sig-name descname"><span class="pre">range_type</span></span><a class="headerlink" href="#django.contrib.postgres.fields.django.contrib.postgres.forms.BaseRangeField.range_type" title="Link to this definition">¶</a></dt>
<dd><p>The range type to use.</p>
</dd></dl>

</dd></dl>

</section>
<section id="s-range-operators">
<span id="range-operators"></span><h3>Range operators<a class="headerlink" href="#range-operators" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.RangeOperators">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RangeOperators</span></span><a class="headerlink" href="#django.contrib.postgres.fields.RangeOperators" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>PostgreSQL provides a set of SQL operators that can be used together with the
range data types (see <a class="reference external" href="https://www.postgresql.org/docs/current/functions-range.html#RANGE-OPERATORS-TABLE">the PostgreSQL documentation for the full details of
range operators</a>). This class is meant as a
convenient method to avoid typos. The operator names overlap with the names of
corresponding lookups.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">RangeOperators</span><span class="p">:</span>
    <span class="n">EQUAL</span> <span class="o">=</span> <span class="s2">&quot;=&quot;</span>
    <span class="n">NOT_EQUAL</span> <span class="o">=</span> <span class="s2">&quot;&lt;&gt;&quot;</span>
    <span class="n">CONTAINS</span> <span class="o">=</span> <span class="s2">&quot;@&gt;&quot;</span>
    <span class="n">CONTAINED_BY</span> <span class="o">=</span> <span class="s2">&quot;&lt;@&quot;</span>
    <span class="n">OVERLAPS</span> <span class="o">=</span> <span class="s2">&quot;&amp;&amp;&quot;</span>
    <span class="n">FULLY_LT</span> <span class="o">=</span> <span class="s2">&quot;&lt;&lt;&quot;</span>
    <span class="n">FULLY_GT</span> <span class="o">=</span> <span class="s2">&quot;&gt;&gt;&quot;</span>
    <span class="n">NOT_LT</span> <span class="o">=</span> <span class="s2">&quot;&amp;&gt;&quot;</span>
    <span class="n">NOT_GT</span> <span class="o">=</span> <span class="s2">&quot;&amp;&lt;&quot;</span>
    <span class="n">ADJACENT_TO</span> <span class="o">=</span> <span class="s2">&quot;-|-&quot;</span>
</pre></div>
</div>
</section>
<section id="s-rangeboundary-expressions">
<span id="rangeboundary-expressions"></span><h3>RangeBoundary() expressions<a class="headerlink" href="#rangeboundary-expressions" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.RangeBoundary">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">RangeBoundary</span></span>(<em class="sig-param"><span class="n"><span class="pre">inclusive_lower</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">inclusive_upper</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>)<a class="headerlink" href="#django.contrib.postgres.fields.RangeBoundary" title="Link to this definition">¶</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.RangeBoundary.inclusive_lower">
<span class="sig-name descname"><span class="pre">inclusive_lower</span></span><a class="headerlink" href="#django.contrib.postgres.fields.RangeBoundary.inclusive_lower" title="Link to this definition">¶</a></dt>
<dd><p>If <code class="docutils literal notranslate"><span class="pre">True</span></code> (default), the lower bound is inclusive <code class="docutils literal notranslate"><span class="pre">'['</span></code>, otherwise
it’s exclusive <code class="docutils literal notranslate"><span class="pre">'('</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="django.contrib.postgres.fields.RangeBoundary.inclusive_upper">
<span class="sig-name descname"><span class="pre">inclusive_upper</span></span><a class="headerlink" href="#django.contrib.postgres.fields.RangeBoundary.inclusive_upper" title="Link to this definition">¶</a></dt>
<dd><p>If <code class="docutils literal notranslate"><span class="pre">False</span></code> (default), the upper bound is exclusive <code class="docutils literal notranslate"><span class="pre">')'</span></code>, otherwise
it’s inclusive <code class="docutils literal notranslate"><span class="pre">']'</span></code>.</p>
</dd></dl>

</dd></dl>

<p>A <code class="docutils literal notranslate"><span class="pre">RangeBoundary()</span></code> expression represents the range boundaries. It can be
used with a custom range functions that expected boundaries, for example to
define <a class="reference internal" href="constraints.html#django.contrib.postgres.constraints.ExclusionConstraint" title="django.contrib.postgres.constraints.ExclusionConstraint"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExclusionConstraint</span></code></a>. See
<a class="reference external" href="https://www.postgresql.org/docs/current/rangetypes.html#RANGETYPES-INCLUSIVITY">the PostgreSQL documentation for the full details</a>.</p>
</section>
</section>
</section>


          </div>
        </div>
      </div>
      
        
          <div class="yui-b" id="sidebar">
            
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">PostgreSQL specific model fields</a><ul>
<li><a class="reference internal" href="#indexing-these-fields">Indexing these fields</a></li>
<li><a class="reference internal" href="#arrayfield"><code class="docutils literal notranslate"><span class="pre">ArrayField</span></code></a><ul>
<li><a class="reference internal" href="#querying-arrayfield">Querying <code class="docutils literal notranslate"><span class="pre">ArrayField</span></code></a><ul>
<li><a class="reference internal" href="#contains"><code class="docutils literal notranslate"><span class="pre">contains</span></code></a></li>
<li><a class="reference internal" href="#contained-by"><code class="docutils literal notranslate"><span class="pre">contained_by</span></code></a></li>
<li><a class="reference internal" href="#overlap"><code class="docutils literal notranslate"><span class="pre">overlap</span></code></a></li>
<li><a class="reference internal" href="#len"><code class="docutils literal notranslate"><span class="pre">len</span></code></a></li>
<li><a class="reference internal" href="#index-transforms">Index transforms</a></li>
<li><a class="reference internal" href="#slice-transforms">Slice transforms</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#hstorefield"><code class="docutils literal notranslate"><span class="pre">HStoreField</span></code></a><ul>
<li><a class="reference internal" href="#querying-hstorefield">Querying <code class="docutils literal notranslate"><span class="pre">HStoreField</span></code></a><ul>
<li><a class="reference internal" href="#key-lookups">Key lookups</a></li>
<li><a class="reference internal" href="#std-fieldlookup-hstorefield.contains"><code class="docutils literal notranslate"><span class="pre">contains</span></code></a></li>
<li><a class="reference internal" href="#std-fieldlookup-hstorefield.contained_by"><code class="docutils literal notranslate"><span class="pre">contained_by</span></code></a></li>
<li><a class="reference internal" href="#has-key"><code class="docutils literal notranslate"><span class="pre">has_key</span></code></a></li>
<li><a class="reference internal" href="#has-any-keys"><code class="docutils literal notranslate"><span class="pre">has_any_keys</span></code></a></li>
<li><a class="reference internal" href="#has-keys"><code class="docutils literal notranslate"><span class="pre">has_keys</span></code></a></li>
<li><a class="reference internal" href="#keys"><code class="docutils literal notranslate"><span class="pre">keys</span></code></a></li>
<li><a class="reference internal" href="#values"><code class="docutils literal notranslate"><span class="pre">values</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#range-fields">Range Fields</a><ul>
<li><a class="reference internal" href="#integerrangefield"><code class="docutils literal notranslate"><span class="pre">IntegerRangeField</span></code></a></li>
<li><a class="reference internal" href="#bigintegerrangefield"><code class="docutils literal notranslate"><span class="pre">BigIntegerRangeField</span></code></a></li>
<li><a class="reference internal" href="#decimalrangefield"><code class="docutils literal notranslate"><span class="pre">DecimalRangeField</span></code></a></li>
<li><a class="reference internal" href="#datetimerangefield"><code class="docutils literal notranslate"><span class="pre">DateTimeRangeField</span></code></a></li>
<li><a class="reference internal" href="#daterangefield"><code class="docutils literal notranslate"><span class="pre">DateRangeField</span></code></a></li>
<li><a class="reference internal" href="#querying-range-fields">Querying Range Fields</a><ul>
<li><a class="reference internal" href="#containment-functions">Containment functions</a><ul>
<li><a class="reference internal" href="#std-fieldlookup-rangefield.contains"><code class="docutils literal notranslate"><span class="pre">contains</span></code></a></li>
<li><a class="reference internal" href="#std-fieldlookup-rangefield.contained_by"><code class="docutils literal notranslate"><span class="pre">contained_by</span></code></a></li>
<li><a class="reference internal" href="#std-fieldlookup-rangefield.overlap"><code class="docutils literal notranslate"><span class="pre">overlap</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#comparison-functions">Comparison functions</a><ul>
<li><a class="reference internal" href="#fully-lt"><code class="docutils literal notranslate"><span class="pre">fully_lt</span></code></a></li>
<li><a class="reference internal" href="#fully-gt"><code class="docutils literal notranslate"><span class="pre">fully_gt</span></code></a></li>
<li><a class="reference internal" href="#not-lt"><code class="docutils literal notranslate"><span class="pre">not_lt</span></code></a></li>
<li><a class="reference internal" href="#not-gt"><code class="docutils literal notranslate"><span class="pre">not_gt</span></code></a></li>
<li><a class="reference internal" href="#adjacent-to"><code class="docutils literal notranslate"><span class="pre">adjacent_to</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#querying-using-the-bounds">Querying using the bounds</a><ul>
<li><a class="reference internal" href="#startswith"><code class="docutils literal notranslate"><span class="pre">startswith</span></code></a></li>
<li><a class="reference internal" href="#endswith"><code class="docutils literal notranslate"><span class="pre">endswith</span></code></a></li>
<li><a class="reference internal" href="#isempty"><code class="docutils literal notranslate"><span class="pre">isempty</span></code></a></li>
<li><a class="reference internal" href="#lower-inc"><code class="docutils literal notranslate"><span class="pre">lower_inc</span></code></a></li>
<li><a class="reference internal" href="#lower-inf"><code class="docutils literal notranslate"><span class="pre">lower_inf</span></code></a></li>
<li><a class="reference internal" href="#upper-inc"><code class="docutils literal notranslate"><span class="pre">upper_inc</span></code></a></li>
<li><a class="reference internal" href="#upper-inf"><code class="docutils literal notranslate"><span class="pre">upper_inf</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#defining-your-own-range-types">Defining your own range types</a></li>
<li><a class="reference internal" href="#range-operators">Range operators</a></li>
<li><a class="reference internal" href="#rangeboundary-expressions">RangeBoundary() expressions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="expressions.html"
                          title="previous chapter">PostgreSQL specific query expressions</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="forms.html"
                          title="next chapter">PostgreSQL specific form fields and widgets</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/ref/contrib/postgres/fields.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
              <h3>Last update:</h3>
              <p class="topless">Jul 02, 2025</p>
          </div>
        
      
    </div>

    <div id="ft">
      <div class="nav">
    &laquo; <a href="expressions.html" title="PostgreSQL specific query expressions">previous</a>
     |
    <a href="../../index.html" title="API Reference" accesskey="U">up</a>
   |
    <a href="forms.html" title="PostgreSQL specific form fields and widgets">next</a> &raquo;</div>
    </div>
  </div>

      <div class="clearer"></div>
    </div>
  </body>
</html>