#!/usr/bin/env python3
"""
Comprehensive docstring checking script for CLEAR project.

This script runs pydocstyle checks and provides detailed reporting
for CI/CD integration and local development.
"""

import argparse
import json
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class DocstringChecker:
    """Comprehensive docstring checker with reporting capabilities."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.apps_dir = project_root / 'apps'
        self.config_dir = project_root / 'config'
        
    def run_pydocstyle(self, paths: List[Path], output_format: str = 'text') -> Tuple[int, str]:
        """Run pydocstyle on specified paths.
        
        Args:
            paths: List of paths to check
            output_format: Output format ('text', 'json')
            
        Returns:
            Tuple of (return_code, output)
        """
        cmd = ['pydocstyle']
        
        if output_format == 'json':
            cmd.append('--format=json')
        
        # Add paths
        cmd.extend([str(path) for path in paths])
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            return result.returncode, result.stdout
        except FileNotFoundError:
            print("Error: pydocstyle not found. Install with: pip install pydocstyle")
            return 1, ""
    
    def parse_pydocstyle_output(self, output: str) -> List[Dict[str, str]]:
        """Parse pydocstyle text output into structured data.
        
        Args:
            output: Raw pydocstyle output
            
        Returns:
            List of issue dictionaries
        """
        issues = []
        lines = output.strip().split('\n')
        
        for line in lines:
            if ':' in line and line.strip():
                parts = line.split(':', 3)
                if len(parts) >= 4:
                    issues.append({
                        'file': parts[0],
                        'line': parts[1],
                        'column': parts[2] if parts[2].isdigit() else '0',
                        'code': parts[3].split()[0] if parts[3].split() else 'Unknown',
                        'message': ' '.join(parts[3].split()[1:]) if len(parts[3].split()) > 1 else parts[3]
                    })
        
        return issues
    
    def categorize_issues(self, issues: List[Dict[str, str]]) -> Dict[str, List[Dict[str, str]]]:
        """Categorize issues by type and severity.
        
        Args:
            issues: List of issue dictionaries
            
        Returns:
            Dictionary of categorized issues
        """
        categories = {
            'missing_docstrings': [],
            'formatting_issues': [],
            'content_issues': [],
            'other': []
        }
        
        missing_docstring_codes = ['D100', 'D101', 'D102', 'D103', 'D104', 'D105', 'D106', 'D107']
        formatting_codes = ['D200', 'D201', 'D202', 'D203', 'D204', 'D205', 'D206', 'D207', 'D208', 'D209', 'D210', 'D211', 'D212', 'D213', 'D214', 'D215']
        content_codes = ['D300', 'D301', 'D302', 'D400', 'D401', 'D402', 'D403', 'D404', 'D405', 'D406', 'D407', 'D408', 'D409', 'D410', 'D411', 'D412', 'D413', 'D414', 'D415', 'D416', 'D417']
        
        for issue in issues:
            code = issue.get('code', '').strip()
            if code in missing_docstring_codes:
                categories['missing_docstrings'].append(issue)
            elif code in formatting_codes:
                categories['formatting_issues'].append(issue)
            elif code in content_codes:
                categories['content_issues'].append(issue)
            else:
                categories['other'].append(issue)
        
        return categories
    
    def generate_report(self, issues: List[Dict[str, str]], output_format: str = 'text') -> str:
        """Generate a comprehensive report of docstring issues.
        
        Args:
            issues: List of issue dictionaries
            output_format: Output format ('text', 'json', 'html')
            
        Returns:
            Formatted report string
        """
        if output_format == 'json':
            return json.dumps({
                'total_issues': len(issues),
                'issues': issues,
                'categories': self.categorize_issues(issues)
            }, indent=2)
        
        if output_format == 'html':
            return self._generate_html_report(issues)
        
        # Text format
        if not issues:
            return "✅ All docstrings are compliant with project standards!"
        
        categories = self.categorize_issues(issues)
        report = []
        
        report.append(f"📋 Docstring Issues Report")
        report.append(f"{'=' * 50}")
        report.append(f"Total Issues: {len(issues)}")
        report.append("")
        
        for category, category_issues in categories.items():
            if category_issues:
                report.append(f"{category.replace('_', ' ').title()}: {len(category_issues)}")
        
        report.append("")
        report.append("Detailed Issues:")
        report.append("-" * 30)
        
        for issue in issues:
            report.append(f"📁 {issue['file']}:{issue['line']}")
            report.append(f"   {issue['code']}: {issue['message']}")
            report.append("")
        
        # Add summary and recommendations
        report.append("📊 Summary by Category:")
        report.append("-" * 30)
        
        for category, category_issues in categories.items():
            if category_issues:
                report.append(f"{category.replace('_', ' ').title()}: {len(category_issues)} issues")
                
                # Show top files with issues
                file_counts = {}
                for issue in category_issues:
                    file_path = issue['file']
                    file_counts[file_path] = file_counts.get(file_path, 0) + 1
                
                top_files = sorted(file_counts.items(), key=lambda x: x[1], reverse=True)[:3]
                if top_files:
                    report.append(f"  Top files:")
                    for file_path, count in top_files:
                        report.append(f"    - {file_path}: {count} issues")
                report.append("")
        
        # Add recommendations
        report.append("💡 Recommendations:")
        report.append("-" * 20)
        
        if categories['missing_docstrings']:
            report.append("• Add docstrings to all public functions, classes, and methods")
            report.append("• Follow Google-style docstring conventions")
        
        if categories['formatting_issues']:
            report.append("• Fix docstring formatting issues")
            report.append("• Ensure proper indentation and line breaks")
        
        if categories['content_issues']:
            report.append("• Improve docstring content quality")
            report.append("• Add missing Args, Returns, and Raises sections")
        
        report.append("")
        report.append("📚 See docs/development/docstring_standards.md for detailed guidelines")
        
        return '\n'.join(report)
    
    def _generate_html_report(self, issues: List[Dict[str, str]]) -> str:
        """Generate HTML report for web viewing."""
        categories = self.categorize_issues(issues)
        
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>CLEAR Project - Docstring Issues Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                .category { margin: 20px 0; }
                .issue { background: #fff5f5; padding: 10px; margin: 5px 0; border-left: 4px solid #ff6b6b; }
                .summary { background: #f0f8ff; padding: 15px; border-radius: 5px; }
                .success { background: #f0fff0; padding: 15px; border-radius: 5px; color: #006400; }
            </style>
        </head>
        <body>
        """
        
        if not issues:
            html += '<div class="success"><h2>✅ All docstrings are compliant!</h2></div>'
        else:
            html += f"""
            <div class="header">
                <h1>📋 Docstring Issues Report</h1>
                <p>Total Issues: {len(issues)}</p>
            </div>
            """
            
            for category, category_issues in categories.items():
                if category_issues:
                    html += f"""
                    <div class="category">
                        <h2>{category.replace('_', ' ').title()} ({len(category_issues)} issues)</h2>
                    """
                    
                    for issue in category_issues:
                        html += f"""
                        <div class="issue">
                            <strong>{issue['file']}:{issue['line']}</strong><br>
                            <code>{issue['code']}</code>: {issue['message']}
                        </div>
                        """
                    
                    html += "</div>"
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def check_all(self, output_format: str = 'text', save_report: bool = False) -> int:
        """Run comprehensive docstring checks on the entire project.
        
        Args:
            output_format: Output format ('text', 'json', 'html')
            save_report: Whether to save report to file
            
        Returns:
            Exit code (0 for success, 1 for issues found)
        """
        paths_to_check = []
        
        if self.apps_dir.exists():
            paths_to_check.append(self.apps_dir)
        
        if self.config_dir.exists():
            paths_to_check.append(self.config_dir)
        
        if not paths_to_check:
            print("No paths found to check")
            return 1
        
        print(f"Checking docstrings in: {', '.join(str(p) for p in paths_to_check)}")
        
        return_code, output = self.run_pydocstyle(paths_to_check)
        
        if return_code == 0 and not output.strip():
            print("✅ All docstrings are compliant with project standards!")
            return 0
        
        issues = self.parse_pydocstyle_output(output)
        report = self.generate_report(issues, output_format)
        
        print(report)
        
        if save_report:
            report_file = self.project_root / f'reports/docstring_report.{output_format}'
            report_file.parent.mkdir(exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"\n📄 Report saved to: {report_file}")
        
        return 1 if issues else 0


def main():
    """Main entry point for the docstring checker."""
    parser = argparse.ArgumentParser(description='Check docstrings in CLEAR project')
    parser.add_argument(
        '--format',
        choices=['text', 'json', 'html'],
        default='text',
        help='Output format'
    )
    parser.add_argument(
        '--save-report',
        action='store_true',
        help='Save report to file'
    )
    parser.add_argument(
        '--ci',
        action='store_true',
        help='CI mode - exit with error code if issues found'
    )
    
    args = parser.parse_args()
    
    project_root = Path(__file__).parent.parent
    checker = DocstringChecker(project_root)
    
    exit_code = checker.check_all(
        output_format=args.format,
        save_report=args.save_report
    )
    
    if args.ci:
        sys.exit(exit_code)
    
    return exit_code


if __name__ == '__main__':
    main()
