#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to enhance type hints across the CLEAR Django project.

This script automatically adds comprehensive type hints to Python files
that are missing them, following the project's type annotation standards.
"""

import ast
import os
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

# Common type imports to add
COMMON_TYPE_IMPORTS = {
    'typing': [
        'Any', 'Dict', 'List', 'Optional', 'Union', 'Tuple', 'ClassVar',
        'Protocol', 'TypeVar', 'Generic', 'Callable'
    ],
    'django.http': [
        'HttpRequest', 'HttpResponse', 'HttpResponseRedirect', 'JsonResponse'
    ],
    'django.db.models': ['QuerySet'],
    'apps.core.types': [
        'DjangoRequest', 'DjangoResponse', 'JSONDict', 'ContextData',
        'TemplateContext', 'PrimaryKey', 'ModelType'
    ]
}

# Django-specific type patterns
DJANGO_TYPE_PATTERNS = {
    'views.py': {
        'function_patterns': [
            (r'def (\w+)\(request,', r'def \1(request: HttpRequest,'),
            (r'def (\w+)\(self, request,', r'def \1(self, request: HttpRequest,'),
        ],
        'return_patterns': [
            (r'return render\(', 'return render('),  # Already returns HttpResponse
            (r'return redirect\(', 'return redirect('),  # Already returns HttpResponseRedirect
            (r'return JsonResponse\(', 'return JsonResponse('),
        ]
    },
    'models.py': {
        'class_patterns': [
            (r'class (\w+)\(models\.Model\):', r'class \1(models.Model):'),
            (r'class (\w+)Manager\(models\.Manager\):', r'class \1Manager(models.Manager[Any]):'),
        ],
        'method_patterns': [
            (r'def __str__\(self\):', r'def __str__(self) -> str:'),
            (r'def save\(self,', r'def save(self,'),
            (r'def delete\(self,', r'def delete(self,'),
        ]
    },
    'forms.py': {
        'class_patterns': [
            (r'class (\w+)\(forms\.Form\):', r'class \1(forms.Form):'),
            (r'class (\w+)\(forms\.ModelForm\):', r'class \1(forms.ModelForm):'),
        ]
    },
    'admin.py': {
        'function_patterns': [
            (r'def (\w+)\(self, request,', r'def \1(self, request: HttpRequest,'),
        ]
    }
}


class TypeHintEnhancer:
    """Enhances Python files with comprehensive type hints."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.apps_dir = project_root / 'apps'
        self.enhanced_files: List[str] = []
        self.skipped_files: List[str] = []
        
    def should_process_file(self, file_path: Path) -> bool:
        """Check if a file should be processed for type hints."""
        if not file_path.suffix == '.py':
            return False
            
        # Skip certain files
        skip_patterns = [
            'migrations/',
            '__pycache__/',
            'tests/',
            'test_',
            'conftest.py',
            'manage.py',
            'wsgi.py',
            'asgi.py',
        ]
        
        file_str = str(file_path)
        return not any(pattern in file_str for pattern in skip_patterns)
    
    def has_type_hints(self, content: str) -> bool:
        """Check if file already has comprehensive type hints."""
        # Look for common type hint patterns
        type_hint_indicators = [
            'from typing import',
            'from __future__ import annotations',
            '-> ',  # Return type annotations
            ': str',
            ': int',
            ': bool',
            ': Optional',
            ': List',
            ': Dict',
            'HttpRequest',
            'HttpResponse',
        ]
        
        return any(indicator in content for indicator in type_hint_indicators)
    
    def add_future_annotations(self, content: str) -> str:
        """Add __future__ annotations import if not present."""
        if 'from __future__ import annotations' in content:
            return content
            
        # Find the first import or add at the top
        lines = content.split('\n')
        insert_index = 0
        
        # Skip docstring and comments
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped and not stripped.startswith('#') and not stripped.startswith('"""') and not stripped.startswith("'''"):
                insert_index = i
                break
        
        lines.insert(insert_index, 'from __future__ import annotations\n')
        return '\n'.join(lines)
    
    def add_type_imports(self, content: str, file_type: str) -> str:
        """Add necessary type imports based on file type."""
        lines = content.split('\n')
        
        # Find where to insert imports
        import_section_end = 0
        for i, line in enumerate(lines):
            if line.strip().startswith('from ') or line.strip().startswith('import '):
                import_section_end = i + 1
        
        # Determine which imports to add based on file content
        needed_imports = set()
        
        if 'HttpRequest' in content or 'request:' in content:
            needed_imports.update(['HttpRequest', 'HttpResponse'])
        
        if 'models.Model' in content or 'models.Manager' in content:
            needed_imports.update(['Any', 'Optional', 'ClassVar'])
        
        if 'def ' in content and '->' not in content:
            needed_imports.update(['Any', 'Optional'])
        
        # Add imports if needed
        if needed_imports:
            typing_imports = [imp for imp in needed_imports if imp in COMMON_TYPE_IMPORTS['typing']]
            if typing_imports:
                import_line = f"from typing import {', '.join(sorted(typing_imports))}"
                lines.insert(import_section_end, import_line)
                import_section_end += 1
            
            django_imports = [imp for imp in needed_imports if imp in COMMON_TYPE_IMPORTS['django.http']]
            if django_imports:
                import_line = f"from django.http import {', '.join(sorted(django_imports))}"
                lines.insert(import_section_end, import_line)
        
        return '\n'.join(lines)
    
    def enhance_function_signatures(self, content: str, file_type: str) -> str:
        """Enhance function signatures with type hints."""
        if file_type not in DJANGO_TYPE_PATTERNS:
            return content
        
        patterns = DJANGO_TYPE_PATTERNS[file_type]
        
        # Apply function patterns
        if 'function_patterns' in patterns:
            for pattern, replacement in patterns['function_patterns']:
                content = re.sub(pattern, replacement, content)
        
        # Apply method patterns
        if 'method_patterns' in patterns:
            for pattern, replacement in patterns['method_patterns']:
                content = re.sub(pattern, replacement, content)
        
        return content
    
    def enhance_class_definitions(self, content: str, file_type: str) -> str:
        """Enhance class definitions with type hints."""
        if file_type not in DJANGO_TYPE_PATTERNS:
            return content
        
        patterns = DJANGO_TYPE_PATTERNS[file_type]
        
        # Apply class patterns
        if 'class_patterns' in patterns:
            for pattern, replacement in patterns['class_patterns']:
                content = re.sub(pattern, replacement, content)
        
        return content
    
    def process_file(self, file_path: Path) -> bool:
        """Process a single file to add type hints."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Skip if already has comprehensive type hints
            if self.has_type_hints(original_content):
                self.skipped_files.append(str(file_path))
                return False
            
            content = original_content
            file_type = file_path.name
            
            # Add future annotations
            content = self.add_future_annotations(content)
            
            # Add type imports
            content = self.add_type_imports(content, file_type)
            
            # Enhance function signatures
            content = self.enhance_function_signatures(content, file_type)
            
            # Enhance class definitions
            content = self.enhance_class_definitions(content, file_type)
            
            # Only write if content changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.enhanced_files.append(str(file_path))
                return True
            else:
                self.skipped_files.append(str(file_path))
                return False
                
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return False
    
    def process_directory(self, directory: Path) -> None:
        """Process all Python files in a directory."""
        for file_path in directory.rglob('*.py'):
            if self.should_process_file(file_path):
                self.process_file(file_path)
    
    def run(self) -> None:
        """Run the type hint enhancement process."""
        print("Starting type hint enhancement...")
        
        # Process all apps
        if self.apps_dir.exists():
            self.process_directory(self.apps_dir)
        
        # Process config directory
        config_dir = self.project_root / 'config'
        if config_dir.exists():
            self.process_directory(config_dir)
        
        # Print summary
        print(f"\nType hint enhancement complete!")
        print(f"Enhanced files: {len(self.enhanced_files)}")
        print(f"Skipped files: {len(self.skipped_files)}")
        
        if self.enhanced_files:
            print("\nEnhanced files:")
            for file_path in self.enhanced_files[:10]:  # Show first 10
                print(f"  - {file_path}")
            if len(self.enhanced_files) > 10:
                print(f"  ... and {len(self.enhanced_files) - 10} more")


def main():
    """Main entry point."""
    project_root = Path(__file__).parent.parent
    enhancer = TypeHintEnhancer(project_root)
    enhancer.run()


if __name__ == '__main__':
    main()
