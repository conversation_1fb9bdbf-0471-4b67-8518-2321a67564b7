#!/usr/bin/env python3
"""
PWA Icon Generator Script
Generates PNG icons in various sizes from the base SVG icon for PWA functionality.
"""

import os
import sys
from pathlib import Path
from typing import List, <PERSON>ple

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from PIL import Image, ImageDraw, ImageFont
    import cairosvg
except ImportError:
    print("Required packages not installed. Please install:")
    print("pip install Pillow cairosvg")
    sys.exit(1)


class PWAIconGenerator:
    """Generates PWA icons from SVG source."""

    def __init__(self, svg_path: Path, output_dir: Path):
        self.svg_path = svg_path
        self.output_dir = output_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Icon sizes required by PWA manifest
        self.icon_sizes = [
            (72, 72),
            (96, 96),
            (128, 128),
            (144, 144),
            (152, 152),
            (192, 192),
            (384, 384),
            (512, 512)
        ]

        # Shortcut icon sizes
        self.shortcut_sizes = [
            (96, 96)
        ]

    def generate_icons(self) -> List[str]:
        """Generate all required PWA icons."""
        generated_files = []

        print(f"Generating PWA icons from {self.svg_path}")

        # Generate main app icons
        for width, height in self.icon_sizes:
            output_file = self.output_dir / f"icon-{width}x{height}.png"
            self._generate_png_from_svg(width, height, output_file)
            generated_files.append(str(output_file))
            print(f"Generated: {output_file}")

        # Generate shortcut icons
        shortcut_icons = [
            ("shortcut-dashboard.png", "#4A90E2", "📊"),
            ("shortcut-map.png", "#50C878", "🗺️"),
            ("shortcut-messages.png", "#FF6B6B", "💬"),
            ("shortcut-projects.png", "#FFD93D", "📁")
        ]

        for filename, color, emoji in shortcut_icons:
            output_file = self.output_dir / filename
            self._generate_shortcut_icon(96, 96, output_file, color, emoji)
            generated_files.append(str(output_file))
            print(f"Generated: {output_file}")

        return generated_files

    def _generate_png_from_svg(self, width: int, height: int, output_file: Path):
        """Convert SVG to PNG with specified dimensions."""
        try:
            # Convert SVG to PNG using cairosvg
            cairosvg.svg2png(
                url=str(self.svg_path),
                write_to=str(output_file),
                output_width=width,
                output_height=height
            )
        except Exception as e:
            print(f"Error generating {output_file}: {e}")
            # Fallback: create a simple colored square with "C"
            self._generate_fallback_icon(width, height, output_file)

    def _generate_fallback_icon(self, width: int, height: int, output_file: Path):
        """Generate a fallback icon if SVG conversion fails."""
        # Create image with green background
        img = Image.new('RGBA', (width, height), (140, 198, 63, 255))
        draw = ImageDraw.Draw(img)

        # Add rounded corners
        self._add_rounded_corners(img, int(width * 0.125))

        # Add "C" text
        try:
            # Try to use a system font
            font_size = int(width * 0.4)
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # Fallback to default font
            font = ImageFont.load_default()

        # Calculate text position
        bbox = draw.textbbox((0, 0), "C", font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (width - text_width) // 2
        y = (height - text_height) // 2

        # Draw text
        draw.text((x, y), "C", fill=(255, 255, 255, 255), font=font)

        # Save image
        img.save(output_file, "PNG")

    def _generate_shortcut_icon(self, width: int, height: int, output_file: Path,
                               color: str, emoji: str):
        """Generate shortcut icons with different colors and symbols."""
        # Convert hex color to RGB
        color_rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))

        # Create image
        img = Image.new('RGBA', (width, height), (*color_rgb, 255))
        draw = ImageDraw.Draw(img)

        # Add rounded corners
        self._add_rounded_corners(img, int(width * 0.125))

        # Add emoji or text
        try:
            font_size = int(width * 0.4)
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()

        # Calculate text position
        bbox = draw.textbbox((0, 0), emoji, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (width - text_width) // 2
        y = (height - text_height) // 2

        # Draw emoji/text
        draw.text((x, y), emoji, fill=(255, 255, 255, 255), font=font)

        # Save image
        img.save(output_file, "PNG")

    def _add_rounded_corners(self, img: Image.Image, radius: int):
        """Add rounded corners to an image."""
        # Create a mask for rounded corners
        mask = Image.new('L', img.size, 0)
        draw = ImageDraw.Draw(mask)
        draw.rounded_rectangle((0, 0) + img.size, radius=radius, fill=255)

        # Apply mask
        img.putalpha(mask)

    def generate_screenshots(self):
        """Generate placeholder screenshots for PWA manifest."""
        screenshots_dir = self.output_dir.parent / "screenshots"
        screenshots_dir.mkdir(exist_ok=True)

        # Generate wide screenshot (1280x720)
        wide_screenshot = screenshots_dir / "dashboard-wide.png"
        self._generate_screenshot(1280, 720, wide_screenshot, "Dashboard Overview")

        # Generate narrow screenshot (750x1334)
        narrow_screenshot = screenshots_dir / "dashboard-narrow.png"
        self._generate_screenshot(750, 1334, narrow_screenshot, "Mobile Dashboard")

        print(f"Generated screenshots in {screenshots_dir}")

    def _generate_screenshot(self, width: int, height: int, output_file: Path, title: str):
        """Generate a placeholder screenshot."""
        # Create image with gradient background
        img = Image.new('RGB', (width, height), (240, 240, 240))
        draw = ImageDraw.Draw(img)

        # Add gradient effect
        for y in range(height):
            color_value = int(240 - (y / height) * 40)
            draw.line([(0, y), (width, y)], fill=(color_value, color_value, color_value))

        # Add title text
        try:
            font_size = min(width, height) // 20
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()

        # Calculate text position
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        x = (width - text_width) // 2
        y = height // 2

        # Draw title
        draw.text((x, y), title, fill=(60, 60, 60), font=font)

        # Add CLEAR branding
        brand_text = "CLEAR Infrastructure Platform"
        bbox = draw.textbbox((0, 0), brand_text, font=font)
        brand_width = bbox[2] - bbox[0]
        brand_x = (width - brand_width) // 2
        brand_y = y + font_size + 20

        draw.text((brand_x, brand_y), brand_text, fill=(140, 198, 63), font=font)

        # Save screenshot
        img.save(output_file, "PNG")


def main():
    """Main function to generate PWA icons."""
    # Set up paths
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    svg_path = project_root / "static" / "images" / "icons" / "icon.svg"
    output_dir = project_root / "static" / "images" / "icons"

    # Check if SVG exists
    if not svg_path.exists():
        print(f"SVG icon not found at {svg_path}")
        return

    # Generate icons
    generator = PWAIconGenerator(svg_path, output_dir)
    generated_files = generator.generate_icons()
    generator.generate_screenshots()

    print(f"\nSuccessfully generated {len(generated_files)} PWA icons!")
    print("Icons are ready for PWA functionality.")

    # Create a summary file
    summary_file = output_dir / "icon_generation_summary.txt"
    with open(summary_file, 'w') as f:
        f.write("PWA Icon Generation Summary\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"Generated {len(generated_files)} icons:\n\n")
        for file_path in generated_files:
            f.write(f"- {Path(file_path).name}\n")
        f.write(f"\nGenerated on: {os.popen('date').read().strip()}\n")

    print(f"Summary saved to: {summary_file}")


if __name__ == "__main__":
    main()
