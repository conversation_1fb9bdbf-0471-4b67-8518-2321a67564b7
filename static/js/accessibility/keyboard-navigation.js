/**
 * Comprehensive Keyboard Navigation System for CLEAR Infrastructure Platform
 * Provides full keyboard accessibility and navigation support
 */

class KeyboardNavigationManager {
    constructor() {
        this.focusableElements = [
            'a[href]',
            'button:not([disabled])',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            '[tabindex]:not([tabindex="-1"])',
            '[contenteditable="true"]',
            'details summary',
            'audio[controls]',
            'video[controls]'
        ].join(', ');

        this.shortcuts = new Map();
        this.focusHistory = [];
        this.skipLinks = [];
        this.isModalOpen = false;

        this.init();
    }

    init() {
        this.setupKeyboardShortcuts();
        this.setupFocusManagement();
        this.setupSkipLinks();
        this.setupModalHandling();
        this.setupHTMXIntegration();
        console.log('[Keyboard Nav] Initialized successfully');
    }

    setupKeyboardShortcuts() {
        // Global shortcuts
        this.registerShortcut('Alt+1', () => this.focusMainContent());
        this.registerShortcut('Alt+2', () => this.focusNavigation());
        this.registerShortcut('Alt+3', () => this.focusSearch());
        this.registerShortcut('Alt+/', () => this.showShortcutsHelp());
        this.registerShortcut('Escape', () => this.handleEscape());

        // Navigation shortcuts
        this.registerShortcut('Alt+ArrowLeft', () => this.navigateBack());
        this.registerShortcut('Alt+ArrowRight', () => this.navigateForward());

        // Application shortcuts
        this.registerShortcut('Alt+d', () => this.focusDashboard());
        this.registerShortcut('Alt+p', () => this.focusProjects());
        this.registerShortcut('Alt+m', () => this.focusMessages());
        this.registerShortcut('Alt+i', () => this.focusInfrastructure());

        // Form shortcuts
        this.registerShortcut('Alt+s', () => this.saveCurrentForm());
        this.registerShortcut('Alt+c', () => this.cancelCurrentForm());

        document.addEventListener('keydown', (event) => {
            this.handleKeyDown(event);
        });
    }

    setupFocusManagement() {
        // Track focus changes
        document.addEventListener('focusin', (event) => {
            this.handleFocusIn(event);
        });

        document.addEventListener('focusout', (event) => {
            this.handleFocusOut(event);
        });

        // Handle tab navigation
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Tab') {
                this.handleTabNavigation(event);
            }
        });

        // Set up focus indicators
        this.setupFocusIndicators();
    }

    setupSkipLinks() {
        // Create skip links container
        const skipLinksContainer = document.createElement('div');
        skipLinksContainer.className = 'skip-links';
        skipLinksContainer.setAttribute('aria-label', 'Skip navigation links');

        // Define skip links
        const skipLinksData = [
            { text: 'Skip to main content', target: '#main-content, main, [role="main"]' },
            { text: 'Skip to navigation', target: 'nav, [role="navigation"]' },
            { text: 'Skip to search', target: '[role="search"], .search-form, #search' },
            { text: 'Skip to footer', target: 'footer, [role="contentinfo"]' }
        ];

        skipLinksData.forEach(linkData => {
            const skipLink = this.createSkipLink(linkData.text, linkData.target);
            skipLinksContainer.appendChild(skipLink);
            this.skipLinks.push(skipLink);
        });

        // Insert skip links at the beginning of the body
        document.body.insertBefore(skipLinksContainer, document.body.firstChild);

        // Add skip link styles
        this.addSkipLinkStyles();
    }

    setupModalHandling() {
        // Handle modal focus trapping
        document.addEventListener('keydown', (event) => {
            if (this.isModalOpen && event.key === 'Tab') {
                this.trapFocusInModal(event);
            }
        });

        // Listen for modal open/close events
        document.addEventListener('show.bs.modal', (event) => {
            this.handleModalOpen(event);
        });

        document.addEventListener('hidden.bs.modal', (event) => {
            this.handleModalClose(event);
        });

        // Handle HTMX modals
        document.addEventListener('htmx:afterSwap', (event) => {
            if (event.target.classList.contains('modal')) {
                this.handleModalOpen(event);
            }
        });
    }

    setupHTMXIntegration() {
        // Handle HTMX content updates
        document.addEventListener('htmx:afterSwap', (event) => {
            this.handleHTMXContentUpdate(event);
        });

        // Handle HTMX loading states
        document.addEventListener('htmx:beforeRequest', (event) => {
            this.handleHTMXBeforeRequest(event);
        });

        document.addEventListener('htmx:afterRequest', (event) => {
            this.handleHTMXAfterRequest(event);
        });
    }

    registerShortcut(keyCombo, callback) {
        this.shortcuts.set(keyCombo, callback);
    }

    handleKeyDown(event) {
        const keyCombo = this.getKeyCombo(event);

        if (this.shortcuts.has(keyCombo)) {
            event.preventDefault();
            this.shortcuts.get(keyCombo)();
            return;
        }

        // Handle arrow key navigation in lists and grids
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
            this.handleArrowKeyNavigation(event);
        }

        // Handle Enter and Space for activation
        if (event.key === 'Enter' || event.key === ' ') {
            this.handleActivationKeys(event);
        }
    }

    getKeyCombo(event) {
        const parts = [];

        if (event.ctrlKey) parts.push('Ctrl');
        if (event.altKey) parts.push('Alt');
        if (event.shiftKey) parts.push('Shift');
        if (event.metaKey) parts.push('Meta');

        parts.push(event.key);

        return parts.join('+');
    }

    handleFocusIn(event) {
        const element = event.target;

        // Add to focus history
        this.focusHistory.push({
            element: element,
            timestamp: Date.now()
        });

        // Limit history size
        if (this.focusHistory.length > 50) {
            this.focusHistory.shift();
        }

        // Announce focus changes for screen readers
        this.announceFocusChange(element);

        // Ensure element is visible
        this.ensureElementVisible(element);
    }

    handleFocusOut(event) {
        // Clean up any temporary focus indicators
        this.cleanupFocusIndicators(event.target);
    }

    handleTabNavigation(event) {
        const focusableElements = this.getFocusableElements();
        const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);

        if (event.shiftKey) {
            // Shift+Tab - move backwards
            if (currentIndex === 0) {
                event.preventDefault();
                focusableElements[focusableElements.length - 1].focus();
            }
        } else {
            // Tab - move forwards
            if (currentIndex === focusableElements.length - 1) {
                event.preventDefault();
                focusableElements[0].focus();
            }
        }
    }

    handleArrowKeyNavigation(event) {
        const element = event.target;
        const parent = element.closest('[role="listbox"], [role="grid"], [role="tablist"], .nav-tabs, .nav-pills');

        if (!parent) return;

        const items = parent.querySelectorAll('[role="option"], [role="gridcell"], [role="tab"], .nav-link');
        const currentIndex = Array.from(items).indexOf(element);

        if (currentIndex === -1) return;

        let nextIndex;

        switch (event.key) {
            case 'ArrowUp':
                nextIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                break;
            case 'ArrowDown':
                nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                break;
            case 'ArrowLeft':
                nextIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                break;
            case 'ArrowRight':
                nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                break;
            default:
                return;
        }

        event.preventDefault();
        items[nextIndex].focus();
    }

    handleActivationKeys(event) {
        const element = event.target;

        // Handle custom interactive elements
        if (element.hasAttribute('data-keyboard-activatable')) {
            event.preventDefault();
            element.click();
            return;
        }

        // Handle HTMX elements
        if (element.hasAttribute('hx-get') || element.hasAttribute('hx-post') ||
            element.hasAttribute('hx-put') || element.hasAttribute('hx-delete')) {
            if (event.key === 'Enter') {
                event.preventDefault();
                element.click();
            }
        }
    }

    createSkipLink(text, targetSelector) {
        const link = document.createElement('a');
        link.href = '#';
        link.className = 'skip-link';
        link.textContent = text;

        link.addEventListener('click', (event) => {
            event.preventDefault();
            this.focusTarget(targetSelector);
        });

        return link;
    }

    focusTarget(selector) {
        const target = document.querySelector(selector);
        if (target) {
            // Make element focusable if it isn't already
            if (!target.hasAttribute('tabindex')) {
                target.setAttribute('tabindex', '-1');
            }

            target.focus();

            // Scroll into view
            target.scrollIntoView({ behavior: 'smooth', block: 'start' });

            // Announce the focus change
            this.announceSkipLinkActivation(target);
        }
    }

    // Shortcut implementations
    focusMainContent() {
        this.focusTarget('#main-content, main, [role="main"]');
    }

    focusNavigation() {
        this.focusTarget('nav, [role="navigation"]');
    }

    focusSearch() {
        this.focusTarget('[role="search"], .search-form, #search, input[type="search"]');
    }

    focusDashboard() {
        const dashboardLink = document.querySelector('a[href*="dashboard"]');
        if (dashboardLink) {
            dashboardLink.click();
        }
    }

    focusProjects() {
        const projectsLink = document.querySelector('a[href*="projects"]');
        if (projectsLink) {
            projectsLink.click();
        }
    }

    focusMessages() {
        const messagesLink = document.querySelector('a[href*="messages"], a[href*="messaging"]');
        if (messagesLink) {
            messagesLink.click();
        }
    }

    focusInfrastructure() {
        const infraLink = document.querySelector('a[href*="infrastructure"]');
        if (infraLink) {
            infraLink.click();
        }
    }

    saveCurrentForm() {
        const form = document.activeElement.closest('form');
        if (form) {
            const saveButton = form.querySelector('button[type="submit"], input[type="submit"]');
            if (saveButton) {
                saveButton.click();
            }
        }
    }

    cancelCurrentForm() {
        const form = document.activeElement.closest('form');
        if (form) {
            const cancelButton = form.querySelector('button[type="button"]:contains("Cancel"), .btn-cancel');
            if (cancelButton) {
                cancelButton.click();
            } else {
                // Try to go back or close modal
                this.handleEscape();
            }
        }
    }

    navigateBack() {
        if (window.history.length > 1) {
            window.history.back();
        }
    }

    navigateForward() {
        window.history.forward();
    }

    handleEscape() {
        // Close modals
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            const closeButton = openModal.querySelector('.btn-close, [data-bs-dismiss="modal"]');
            if (closeButton) {
                closeButton.click();
            }
            return;
        }

        // Close dropdowns
        const openDropdown = document.querySelector('.dropdown-menu.show');
        if (openDropdown) {
            const toggle = document.querySelector('[data-bs-toggle="dropdown"][aria-expanded="true"]');
            if (toggle) {
                toggle.click();
            }
            return;
        }

        // Hide help overlay
        this.hideShortcutsHelp();
    }

    showShortcutsHelp() {
        // Create or show shortcuts help overlay
        let helpOverlay = document.getElementById('keyboard-shortcuts-help');

        if (!helpOverlay) {
            helpOverlay = this.createShortcutsHelpOverlay();
            document.body.appendChild(helpOverlay);
        }

        helpOverlay.classList.remove('d-none');
        helpOverlay.focus();
    }

    hideShortcutsHelp() {
        const helpOverlay = document.getElementById('keyboard-shortcuts-help');
        if (helpOverlay) {
            helpOverlay.classList.add('d-none');
        }
    }

    createShortcutsHelpOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'keyboard-shortcuts-help';
        overlay.className = 'keyboard-shortcuts-help d-none';
        overlay.setAttribute('role', 'dialog');
        overlay.setAttribute('aria-labelledby', 'shortcuts-title');
        overlay.setAttribute('tabindex', '-1');

        overlay.innerHTML = `
            <div class="shortcuts-content">
                <div class="shortcuts-header">
                    <h2 id="shortcuts-title">Keyboard Shortcuts</h2>
                    <button class="btn-close" aria-label="Close shortcuts help"></button>
                </div>
                <div class="shortcuts-body">
                    <div class="shortcuts-section">
                        <h3>Navigation</h3>
                        <dl>
                            <dt>Alt + 1</dt><dd>Focus main content</dd>
                            <dt>Alt + 2</dt><dd>Focus navigation</dd>
                            <dt>Alt + 3</dt><dd>Focus search</dd>
                            <dt>Alt + ←</dt><dd>Go back</dd>
                            <dt>Alt + →</dt><dd>Go forward</dd>
                        </dl>
                    </div>
                    <div class="shortcuts-section">
                        <h3>Application</h3>
                        <dl>
                            <dt>Alt + d</dt><dd>Go to dashboard</dd>
                            <dt>Alt + p</dt><dd>Go to projects</dd>
                            <dt>Alt + m</dt><dd>Go to messages</dd>
                            <dt>Alt + i</dt><dd>Go to infrastructure</dd>
                        </dl>
                    </div>
                    <div class="shortcuts-section">
                        <h3>Forms</h3>
                        <dl>
                            <dt>Alt + s</dt><dd>Save current form</dd>
                            <dt>Alt + c</dt><dd>Cancel current form</dd>
                        </dl>
                    </div>
                    <div class="shortcuts-section">
                        <h3>General</h3>
                        <dl>
                            <dt>Escape</dt><dd>Close modal/dropdown</dd>
                            <dt>Alt + /</dt><dd>Show this help</dd>
                        </dl>
                    </div>
                </div>
            </div>
        `;

        // Add close functionality
        const closeButton = overlay.querySelector('.btn-close');
        closeButton.addEventListener('click', () => this.hideShortcutsHelp());

        // Close on escape
        overlay.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.hideShortcutsHelp();
            }
        });

        return overlay;
    }

    handleModalOpen(event) {
        this.isModalOpen = true;
        const modal = event.target;

        // Store the element that opened the modal
        this.modalTrigger = document.activeElement;

        // Focus the first focusable element in the modal
        setTimeout(() => {
            const firstFocusable = modal.querySelector(this.focusableElements);
            if (firstFocusable) {
                firstFocusable.focus();
            }
        }, 100);
    }

    handleModalClose(event) {
        this.isModalOpen = false;

        // Return focus to the trigger element
        if (this.modalTrigger) {
            this.modalTrigger.focus();
            this.modalTrigger = null;
        }
    }

    trapFocusInModal(event) {
        const modal = document.querySelector('.modal.show');
        if (!modal) return;

        const focusableElements = modal.querySelectorAll(this.focusableElements);
        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];

        if (event.shiftKey) {
            if (document.activeElement === firstFocusable) {
                event.preventDefault();
                lastFocusable.focus();
            }
        } else {
            if (document.activeElement === lastFocusable) {
                event.preventDefault();
                firstFocusable.focus();
            }
        }
    }

    handleHTMXContentUpdate(event) {
        const newContent = event.target;

        // Set up keyboard navigation for new content
        this.setupKeyboardNavigationForElement(newContent);

        // Announce content update to screen readers
        this.announceContentUpdate(newContent);
    }

    handleHTMXBeforeRequest(event) {
        // Add loading announcement
        this.announceLoading(event.target);
    }

    handleHTMXAfterRequest(event) {
        // Announce completion
        this.announceLoadingComplete(event.target);
    }

    setupKeyboardNavigationForElement(element) {
        // Add keyboard activation to interactive elements
        const interactiveElements = element.querySelectorAll('[hx-get], [hx-post], [hx-put], [hx-delete]');

        interactiveElements.forEach(el => {
            if (!el.hasAttribute('tabindex') && !this.isFocusable(el)) {
                el.setAttribute('tabindex', '0');
                el.setAttribute('data-keyboard-activatable', 'true');
            }
        });

        // Set up arrow key navigation for lists
        const lists = element.querySelectorAll('[role="listbox"], [role="grid"], .list-group');
        lists.forEach(list => {
            this.setupArrowKeyNavigation(list);
        });
    }

    setupArrowKeyNavigation(container) {
        const items = container.querySelectorAll('[role="option"], [role="gridcell"], .list-group-item');

        items.forEach((item, index) => {
            if (!item.hasAttribute('tabindex')) {
                item.setAttribute('tabindex', index === 0 ? '0' : '-1');
            }
        });
    }

    getFocusableElements() {
        return document.querySelectorAll(this.focusableElements);
    }

    isFocusable(element) {
        return element.matches(this.focusableElements);
    }

    ensureElementVisible(element) {
        // Check if element is visible in viewport
        const rect = element.getBoundingClientRect();
        const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;

        if (!isVisible) {
            element.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    setupFocusIndicators() {
        // Add enhanced focus indicators
        const style = document.createElement('style');
        style.textContent = `
            .keyboard-focus-indicator {
                outline: 3px solid #0066cc !important;
                outline-offset: 2px !important;
                box-shadow: 0 0 0 1px #ffffff !important;
            }
        `;
        document.head.appendChild(style);

        // Add focus indicator class on keyboard navigation
        document.addEventListener('keydown', () => {
            document.body.classList.add('keyboard-navigation');
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }

    cleanupFocusIndicators(element) {
        element.classList.remove('keyboard-focus-indicator');
    }

    // Accessibility announcements
    announceFocusChange(element) {
        const announcement = this.getFocusAnnouncement(element);
        if (announcement) {
            this.announceToScreenReader(announcement);
        }
    }

    getFocusAnnouncement(element) {
        // Get appropriate announcement text
        const label = element.getAttribute('aria-label') ||
                     element.getAttribute('title') ||
                     element.textContent?.trim() ||
                     element.getAttribute('placeholder');

        const role = element.getAttribute('role') || element.tagName.toLowerCase();

        if (label) {
            return `${label}, ${role}`;
        }

        return null;
    }

    announceSkipLinkActivation(target) {
        const text = target.getAttribute('aria-label') ||
                    target.textContent?.trim() ||
                    'Content area';
        this.announceToScreenReader(`Skipped to ${text}`);
    }

    announceContentUpdate(element) {
        this.announceToScreenReader('Content updated');
    }

    announceLoading(element) {
        this.announceToScreenReader('Loading');
    }

    announceLoadingComplete(element) {
        this.announceToScreenReader('Loading complete');
    }

    announceToScreenReader(message) {
        // Create or update live region
        let liveRegion = document.getElementById('keyboard-nav-announcements');

        if (!liveRegion) {
            liveRegion = document.createElement('div');
            liveRegion.id = 'keyboard-nav-announcements';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'visually-hidden';
            document.body.appendChild(liveRegion);
        }

        // Clear and set new message
        liveRegion.textContent = '';
        setTimeout(() => {
            liveRegion.textContent = message;
        }, 100);
    }

    addSkipLinkStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .skip-links {
                position: absolute;
                top: -40px;
                left: 6px;
                z-index: 1000;
            }

            .skip-link {
                position: absolute;
                top: -40px;
                left: 0;
                background: #000;
                color: #fff;
                padding: 8px 16px;
                text-decoration: none;
                border-radius: 0 0 4px 4px;
                font-weight: bold;
                transition: top 0.3s;
            }

            .skip-link:focus {
                top: 0;
                color: #fff;
                text-decoration: underline;
            }

            .keyboard-shortcuts-help {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                z-index: 1060;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .shortcuts-content {
                background: white;
                border-radius: 8px;
                padding: 2rem;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
            }

            .shortcuts-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1.5rem;
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 1rem;
            }

            .shortcuts-body {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
            }

            .shortcuts-section h3 {
                margin-bottom: 0.5rem;
                color: #495057;
            }

            .shortcuts-section dl {
                margin: 0;
            }

            .shortcuts-section dt {
                font-family: monospace;
                background: #f8f9fa;
                padding: 2px 6px;
                border-radius: 3px;
                font-weight: bold;
                display: inline-block;
                min-width: 60px;
            }

            .shortcuts-section dd {
                margin-left: 0;
                margin-bottom: 0.5rem;
                padding-left: 0.5rem;
                display: inline-block;
            }

            .visually-hidden {
                position: absolute !important;
                width: 1px !important;
                height: 1px !important;
                padding: 0 !important;
                margin: -1px !important;
                overflow: hidden !important;
                clip: rect(0, 0, 0, 0) !important;
                white-space: nowrap !important;
                border: 0 !important;
            }

            body.keyboard-navigation *:focus {
                outline: 3px solid #0066cc !important;
                outline-offset: 2px !important;
            }
        `;

        document.head.appendChild(style);
    }
}

// Initialize keyboard navigation
document.addEventListener('DOMContentLoaded', () => {
    window.keyboardNav = new KeyboardNavigationManager();
    console.log('[Keyboard Nav] Manager initialized');
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = KeyboardNavigationManager;
}
