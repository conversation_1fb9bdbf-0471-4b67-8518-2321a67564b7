/**
 * Offline HTMX Integration for CLEAR Infrastructure Platform
 * Provides seamless offline functionality for HTMX requests and responses
 */

class OfflineHTMXIntegration {
    constructor() {
        this.offlineStorage = null;
        this.isOnline = navigator.onLine;
        this.pendingRequests = new Map();
        this.offlineIndicators = new Map();

        this.init();
    }

    init() {
        // Wait for offline storage to be available
        this.waitForOfflineStorage().then(() => {
            this.setupHTMXIntegration();
            this.setupEventListeners();
            this.createOfflineIndicators();
            console.log('[Offline HTMX] Integration initialized');
        });
    }

    async waitForOfflineStorage() {
        return new Promise((resolve) => {
            const checkStorage = () => {
                if (window.offlineStorage) {
                    this.offlineStorage = window.offlineStorage;
                    resolve();
                } else {
                    setTimeout(checkStorage, 100);
                }
            };
            checkStorage();
        });
    }

    setupHTMXIntegration() {
        // Intercept HTMX requests before they're sent
        document.addEventListener('htmx:beforeRequest', (event) => {
            this.handleBeforeRequest(event);
        });

        // Handle successful HTMX responses
        document.addEventListener('htmx:afterRequest', (event) => {
            this.handleAfterRequest(event);
        });

        // Handle HTMX errors (including network failures)
        document.addEventListener('htmx:responseError', (event) => {
            this.handleResponseError(event);
        });

        // Handle HTMX send errors (network unavailable)
        document.addEventListener('htmx:sendError', (event) => {
            this.handleSendError(event);
        });

        // Handle successful swaps
        document.addEventListener('htmx:afterSwap', (event) => {
            this.handleAfterSwap(event);
        });

        // Handle before swap to check for offline content
        document.addEventListener('htmx:beforeSwap', (event) => {
            this.handleBeforeSwap(event);
        });
    }

    setupEventListeners() {
        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.handleOnlineStatusChange(true);
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.handleOnlineStatusChange(false);
        });

        // Listen for form submissions
        document.addEventListener('submit', (event) => {
            if (!this.isOnline) {
                this.handleOfflineFormSubmission(event);
            }
        });
    }

    async handleBeforeRequest(event) {
        const requestConfig = event.detail;
        const url = requestConfig.path;
        const method = requestConfig.verb.toUpperCase();

        // Store request info for potential offline handling
        this.pendingRequests.set(requestConfig.requestId || url, {
            url: url,
            method: method,
            element: event.target,
            timestamp: Date.now()
        });

        // If offline, try to handle the request
        if (!this.isOnline) {
            await this.handleOfflineRequest(event);
        }
    }

    async handleOfflineRequest(event) {
        const requestConfig = event.detail;
        const url = requestConfig.path;
        const method = requestConfig.verb.toUpperCase();
        const element = event.target;

        console.log(`[Offline HTMX] Handling offline request: ${method} ${url}`);

        // For GET requests, try to serve from cache
        if (method === 'GET') {
            const cachedContent = await this.getCachedContent(url);
            if (cachedContent) {
                event.preventDefault();
                this.simulateHTMXResponse(element, cachedContent, url);
                return;
            }
        }

        // For POST/PUT/DELETE requests, queue them
        if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
            event.preventDefault();
            await this.queueOfflineRequest(requestConfig, element);
            return;
        }

        // If we can't handle it offline, prevent the request and show message
        event.preventDefault();
        this.showOfflineMessage(element, 'This content is not available offline');
    }

    async handleAfterRequest(event) {
        const requestConfig = event.detail;
        const url = requestConfig.path;
        const method = requestConfig.verb.toUpperCase();
        const xhr = requestConfig.xhr;

        // If request was successful and we're online, cache the response
        if (this.isOnline && xhr.status >= 200 && xhr.status < 300 && method === 'GET') {
            await this.cacheResponse(url, xhr.responseText);
        }

        // Clean up pending request
        this.pendingRequests.delete(requestConfig.requestId || url);
    }

    async handleResponseError(event) {
        const requestConfig = event.detail;
        const url = requestConfig.path;
        const method = requestConfig.verb.toUpperCase();
        const element = event.target;

        console.log(`[Offline HTMX] Response error for: ${method} ${url}`);

        // If it's a network error and we have cached content, use it
        if (method === 'GET') {
            const cachedContent = await this.getCachedContent(url);
            if (cachedContent) {
                this.simulateHTMXResponse(element, cachedContent, url, true);
                return;
            }
        }

        // Show offline error message
        this.showOfflineMessage(element, 'Unable to load content. Please check your connection.');
    }

    async handleSendError(event) {
        const requestConfig = event.detail;
        const url = requestConfig.path;
        const method = requestConfig.verb.toUpperCase();
        const element = event.target;

        console.log(`[Offline HTMX] Send error for: ${method} ${url}`);

        // Try to handle as offline request
        if (method === 'GET') {
            const cachedContent = await this.getCachedContent(url);
            if (cachedContent) {
                this.simulateHTMXResponse(element, cachedContent, url, true);
                return;
            }
        }

        // Queue non-GET requests
        if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
            await this.queueOfflineRequest(requestConfig, element);
            return;
        }

        this.showOfflineMessage(element, 'Network unavailable. Content will load when connection is restored.');
    }

    handleAfterSwap(event) {
        const element = event.target;

        // Add offline indicators to newly swapped content
        this.addOfflineIndicatorsToElement(element);

        // Set up offline-aware forms in the new content
        this.setupOfflineFormsInElement(element);
    }

    handleBeforeSwap(event) {
        const responseText = event.detail.xhr.responseText;

        // Check if response indicates offline content
        if (responseText.includes('data-offline-content="true"')) {
            // Add offline indicator class
            event.detail.target.classList.add('offline-content');
        }
    }

    async getCachedContent(url) {
        try {
            // Try to get from offline storage cache metadata
            const cached = await this.offlineStorage.getCachedResponse(url);
            if (cached && cached.content) {
                return cached.content;
            }

            // Try to get from service worker cache
            if ('caches' in window) {
                const cache = await caches.open('clear-pwa-v1.0.0-dynamic');
                const response = await cache.match(url);
                if (response) {
                    return await response.text();
                }
            }
        } catch (error) {
            console.error('[Offline HTMX] Error getting cached content:', error);
        }

        return null;
    }

    async cacheResponse(url, content) {
        try {
            // Cache in offline storage
            await this.offlineStorage.storeData('cacheMetadata', {
                key: url,
                content: content,
                timestamp: Date.now(),
                expiry: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
            });

            console.log(`[Offline HTMX] Cached response for: ${url}`);
        } catch (error) {
            console.error('[Offline HTMX] Error caching response:', error);
        }
    }

    async queueOfflineRequest(requestConfig, element) {
        const requestData = {
            url: requestConfig.path,
            method: requestConfig.verb.toUpperCase(),
            headers: requestConfig.headers || {},
            body: requestConfig.body || null,
            timestamp: Date.now(),
            priority: this.getRequestPriority(element),
            elementId: element.id || null,
            targetSelector: element.getAttribute('hx-target') || null
        };

        try {
            await this.offlineStorage.storeData('formQueue', requestData);

            this.showOfflineMessage(
                element,
                'Request saved. It will be sent when you\'re back online.',
                'info'
            );

            // Add visual indicator that request is queued
            this.addQueuedIndicator(element);

        } catch (error) {
            console.error('[Offline HTMX] Error queuing request:', error);
            this.showOfflineMessage(element, 'Unable to save request offline', 'error');
        }
    }

    simulateHTMXResponse(element, content, url, isFromCache = false) {
        // Get target element
        const targetSelector = element.getAttribute('hx-target');
        const target = targetSelector ?
            document.querySelector(targetSelector) : element;

        if (!target) {
            console.error('[Offline HTMX] Target element not found');
            return;
        }

        // Get swap method
        const swapMethod = element.getAttribute('hx-swap') || 'innerHTML';

        // Add offline indicator to content
        const offlineContent = isFromCache ?
            this.addOfflineIndicatorToContent(content) : content;

        // Perform the swap
        this.performSwap(target, offlineContent, swapMethod);

        // Trigger HTMX events
        target.dispatchEvent(new CustomEvent('htmx:afterSwap', {
            detail: {
                target: target,
                requestConfig: { path: url },
                xhr: { responseText: offlineContent }
            }
        }));

        console.log(`[Offline HTMX] Simulated response for: ${url}`);
    }

    performSwap(target, content, swapMethod) {
        switch (swapMethod) {
            case 'innerHTML':
                target.innerHTML = content;
                break;
            case 'outerHTML':
                target.outerHTML = content;
                break;
            case 'beforebegin':
                target.insertAdjacentHTML('beforebegin', content);
                break;
            case 'afterbegin':
                target.insertAdjacentHTML('afterbegin', content);
                break;
            case 'beforeend':
                target.insertAdjacentHTML('beforeend', content);
                break;
            case 'afterend':
                target.insertAdjacentHTML('afterend', content);
                break;
            case 'delete':
                target.remove();
                break;
            case 'none':
                // Do nothing
                break;
            default:
                target.innerHTML = content;
        }
    }

    addOfflineIndicatorToContent(content) {
        const offlineIndicator = `
            <div class="offline-content-indicator">
                <small class="text-muted">
                    <i class="fas fa-wifi-slash me-1"></i>
                    Showing cached content
                </small>
            </div>
        `;

        return offlineIndicator + content;
    }

    showOfflineMessage(element, message, type = 'warning') {
        // Get target element
        const targetSelector = element.getAttribute('hx-target');
        const target = targetSelector ?
            document.querySelector(targetSelector) : element;

        if (!target) return;

        const alertClass = `alert-${type}`;
        const iconClass = type === 'error' ? 'fa-exclamation-triangle' :
                         type === 'info' ? 'fa-info-circle' : 'fa-wifi-slash';

        const messageHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show offline-message" role="alert">
                <i class="fas ${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Insert message at the beginning of target
        target.insertAdjacentHTML('afterbegin', messageHTML);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            const messageEl = target.querySelector('.offline-message');
            if (messageEl) {
                messageEl.remove();
            }
        }, 5000);
    }

    addQueuedIndicator(element) {
        element.classList.add('offline-queued');

        // Add visual indicator
        const indicator = document.createElement('span');
        indicator.className = 'offline-queue-indicator';
        indicator.innerHTML = '<i class="fas fa-clock text-warning"></i>';
        indicator.title = 'Request queued for when online';

        element.appendChild(indicator);
    }

    removeQueuedIndicator(element) {
        element.classList.remove('offline-queued');
        const indicator = element.querySelector('.offline-queue-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    getRequestPriority(element) {
        if (element.classList.contains('priority-high')) return 'high';
        if (element.classList.contains('priority-low')) return 'low';
        return 'normal';
    }

    handleOnlineStatusChange(isOnline) {
        console.log(`[Offline HTMX] Connection status changed: ${isOnline ? 'online' : 'offline'}`);

        // Update all offline indicators
        this.updateOfflineIndicators(isOnline);

        // If back online, process queued requests
        if (isOnline) {
            this.processQueuedRequests();
        }
    }

    async processQueuedRequests() {
        try {
            const queuedRequests = await this.offlineStorage.getAllData('formQueue');

            for (const request of queuedRequests) {
                if (request.isHTMX) {
                    await this.replayHTMXRequest(request);
                }
            }

        } catch (error) {
            console.error('[Offline HTMX] Error processing queued requests:', error);
        }
    }

    async replayHTMXRequest(request) {
        try {
            const response = await fetch(request.url, {
                method: request.method,
                headers: {
                    ...request.headers,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: request.body
            });

            if (response.ok) {
                // Remove from queue
                await this.offlineStorage.deleteData('formQueue', request.id);

                // Update UI if element still exists
                if (request.elementId) {
                    const element = document.getElementById(request.elementId);
                    if (element) {
                        this.removeQueuedIndicator(element);
                    }
                }

                console.log(`[Offline HTMX] Successfully replayed request: ${request.url}`);
            }

        } catch (error) {
            console.error('[Offline HTMX] Error replaying request:', error);
        }
    }

    createOfflineIndicators() {
        // Create global offline status indicator
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'offline-status-indicator';
        statusIndicator.className = 'offline-status-indicator d-none';
        statusIndicator.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-wifi-slash me-2"></i>
                <span>You're offline</span>
                <button class="btn btn-sm btn-outline-light ms-2" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        `;

        document.body.appendChild(statusIndicator);

        // Add styles
        this.addOfflineStyles();

        // Update initial state
        this.updateOfflineIndicators(this.isOnline);
    }

    updateOfflineIndicators(isOnline) {
        const statusIndicator = document.getElementById('offline-status-indicator');

        if (statusIndicator) {
            if (isOnline) {
                statusIndicator.classList.add('d-none');
            } else {
                statusIndicator.classList.remove('d-none');
            }
        }

        // Update all offline content indicators
        const offlineContent = document.querySelectorAll('.offline-content');
        offlineContent.forEach(element => {
            if (isOnline) {
                element.classList.remove('offline-content');
            }
        });
    }

    addOfflineIndicatorsToElement(element) {
        // Add offline indicators to forms and interactive elements
        const forms = element.querySelectorAll('form');
        forms.forEach(form => {
            this.setupOfflineForm(form);
        });

        const htmxElements = element.querySelectorAll('[hx-get], [hx-post], [hx-put], [hx-delete]');
        htmxElements.forEach(el => {
            this.setupOfflineHTMXElement(el);
        });
    }

    setupOfflineFormsInElement(element) {
        const forms = element.querySelectorAll('form');
        forms.forEach(form => {
            this.setupOfflineForm(form);
        });
    }

    setupOfflineForm(form) {
        // Add offline handling class
        form.classList.add('offline-aware');

        // Add submit handler for offline scenarios
        form.addEventListener('submit', (event) => {
            if (!this.isOnline) {
                this.handleOfflineFormSubmission(event);
            }
        });
    }

    setupOfflineHTMXElement(element) {
        // Add offline handling class
        element.classList.add('offline-aware');

        // Add visual indicator when offline
        if (!this.isOnline) {
            element.classList.add('offline-disabled');
        }
    }

    handleOfflineFormSubmission(event) {
        // Let the offline storage manager handle this
        // This is just for additional UI feedback
        const form = event.target;

        // Add visual feedback
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Saved for later...';

            setTimeout(() => {
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            }, 2000);
        }
    }

    addOfflineStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .offline-status-indicator {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: #dc3545;
                color: white;
                padding: 0.5rem;
                text-align: center;
                z-index: 1070;
                font-size: 0.875rem;
                animation: slideDown 0.3s ease-out;
            }

            .offline-content {
                position: relative;
                opacity: 0.8;
            }

            .offline-content::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, #ffc107, #fd7e14);
                z-index: 1;
            }

            .offline-content-indicator {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 0.25rem 0.5rem;
                margin-bottom: 0.5rem;
                border-radius: 0.25rem;
            }

            .offline-queued {
                position: relative;
                opacity: 0.7;
            }

            .offline-queue-indicator {
                position: absolute;
                top: -5px;
                right: -5px;
                background: white;
                border-radius: 50%;
                padding: 2px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }

            .offline-disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            .offline-message {
                margin-bottom: 1rem;
            }

            @keyframes slideDown {
                from { transform: translateY(-100%); }
                to { transform: translateY(0); }
            }
        `;

        document.head.appendChild(style);
    }
}

// Initialize offline HTMX integration
document.addEventListener('DOMContentLoaded', () => {
    window.offlineHTMX = new OfflineHTMXIntegration();
    console.log('[Offline HTMX] Integration ready');
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OfflineHTMXIntegration;
}
