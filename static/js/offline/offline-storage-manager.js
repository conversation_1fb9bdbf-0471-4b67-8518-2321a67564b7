/**
 * Offline Storage Manager for CLEAR Infrastructure Platform
 * Manages offline data storage, form submissions, and synchronization
 */

class OfflineStorageManager {
    constructor() {
        this.dbName = 'CLEAR_OfflineDB';
        this.dbVersion = 1;
        this.db = null;
        this.isOnline = navigator.onLine;
        this.syncQueue = [];
        this.maxOfflineEntries = 1000;

        this.init();
    }

    async init() {
        try {
            await this.initDatabase();
            this.setupEventListeners();
            this.startPeriodicSync();
            console.log('[Offline Storage] Initialized successfully');
        } catch (error) {
            console.error('[Offline Storage] Initialization failed:', error);
        }
    }

    async initDatabase() {
        return new Promise((resolve, reject) => {
            if (!('indexedDB' in window)) {
                reject(new Error('IndexedDB not supported'));
                return;
            }

            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('Failed to open IndexedDB'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Create object stores for different types of offline data

                // Projects store
                if (!db.objectStoreNames.contains('projects')) {
                    const projectStore = db.createObjectStore('projects', { keyPath: 'id' });
                    projectStore.createIndex('name', 'name', { unique: false });
                    projectStore.createIndex('lastModified', 'lastModified', { unique: false });
                }

                // Documents store
                if (!db.objectStoreNames.contains('documents')) {
                    const docStore = db.createObjectStore('documents', { keyPath: 'id' });
                    docStore.createIndex('projectId', 'projectId', { unique: false });
                    docStore.createIndex('type', 'type', { unique: false });
                }

                // Infrastructure data store
                if (!db.objectStoreNames.contains('infrastructure')) {
                    const infraStore = db.createObjectStore('infrastructure', { keyPath: 'id' });
                    infraStore.createIndex('type', 'type', { unique: false });
                    infraStore.createIndex('location', 'location', { unique: false });
                }

                // Messages store
                if (!db.objectStoreNames.contains('messages')) {
                    const msgStore = db.createObjectStore('messages', { keyPath: 'id' });
                    msgStore.createIndex('threadId', 'threadId', { unique: false });
                    msgStore.createIndex('timestamp', 'timestamp', { unique: false });
                }

                // Form submissions queue
                if (!db.objectStoreNames.contains('formQueue')) {
                    const formStore = db.createObjectStore('formQueue', { keyPath: 'id', autoIncrement: true });
                    formStore.createIndex('timestamp', 'timestamp', { unique: false });
                    formStore.createIndex('priority', 'priority', { unique: false });
                }

                // Offline notes/drafts
                if (!db.objectStoreNames.contains('drafts')) {
                    const draftStore = db.createObjectStore('drafts', { keyPath: 'id', autoIncrement: true });
                    draftStore.createIndex('type', 'type', { unique: false });
                    draftStore.createIndex('lastModified', 'lastModified', { unique: false });
                }

                // Cache metadata
                if (!db.objectStoreNames.contains('cacheMetadata')) {
                    const metaStore = db.createObjectStore('cacheMetadata', { keyPath: 'key' });
                    metaStore.createIndex('expiry', 'expiry', { unique: false });
                }
            };
        });
    }

    setupEventListeners() {
        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('[Offline Storage] Back online - starting sync');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('[Offline Storage] Gone offline - enabling offline mode');
        });

        // Intercept form submissions when offline
        document.addEventListener('submit', (event) => {
            if (!this.isOnline) {
                this.handleOfflineFormSubmission(event);
            }
        });

        // Intercept HTMX requests when offline
        document.addEventListener('htmx:beforeRequest', (event) => {
            if (!this.isOnline) {
                this.handleOfflineHTMXRequest(event);
            }
        });
    }

    // Core storage operations
    async storeData(storeName, data) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);

            // Add timestamp and offline flag
            const dataWithMeta = {
                ...data,
                lastModified: Date.now(),
                isOfflineCreated: !this.isOnline
            };

            const request = store.put(dataWithMeta);

            request.onsuccess = () => {
                console.log(`[Offline Storage] Stored data in ${storeName}:`, data.id || data.name);
                resolve(request.result);
            };

            request.onerror = () => {
                reject(new Error(`Failed to store data in ${storeName}`));
            };
        });
    }

    async getData(storeName, key) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(key);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(new Error(`Failed to get data from ${storeName}`));
            };
        });
    }

    async getAllData(storeName, indexName = null, indexValue = null) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);

            let request;
            if (indexName && indexValue) {
                const index = store.index(indexName);
                request = index.getAll(indexValue);
            } else {
                request = store.getAll();
            }

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(new Error(`Failed to get all data from ${storeName}`));
            };
        });
    }

    async deleteData(storeName, key) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(key);

            request.onsuccess = () => {
                resolve();
            };

            request.onerror = () => {
                reject(new Error(`Failed to delete data from ${storeName}`));
            };
        });
    }

    // Critical feature implementations
    async storeProject(project) {
        try {
            await this.storeData('projects', project);
            this.showOfflineNotification('Project saved offline', 'success');
        } catch (error) {
            console.error('[Offline Storage] Failed to store project:', error);
            this.showOfflineNotification('Failed to save project offline', 'error');
        }
    }

    async getOfflineProjects() {
        try {
            return await this.getAllData('projects');
        } catch (error) {
            console.error('[Offline Storage] Failed to get offline projects:', error);
            return [];
        }
    }

    async storeDocument(document) {
        try {
            await this.storeData('documents', document);
            this.showOfflineNotification('Document saved offline', 'success');
        } catch (error) {
            console.error('[Offline Storage] Failed to store document:', error);
        }
    }

    async storeInfrastructureData(infraData) {
        try {
            await this.storeData('infrastructure', infraData);
            this.showOfflineNotification('Infrastructure data cached', 'info');
        } catch (error) {
            console.error('[Offline Storage] Failed to store infrastructure data:', error);
        }
    }

    async storeMessage(message) {
        try {
            await this.storeData('messages', message);
        } catch (error) {
            console.error('[Offline Storage] Failed to store message:', error);
        }
    }

    async getOfflineMessages(threadId = null) {
        try {
            if (threadId) {
                return await this.getAllData('messages', 'threadId', threadId);
            }
            return await this.getAllData('messages');
        } catch (error) {
            console.error('[Offline Storage] Failed to get offline messages:', error);
            return [];
        }
    }

    // Form submission handling
    async handleOfflineFormSubmission(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const formObject = Object.fromEntries(formData.entries());

        // Create form submission entry
        const submission = {
            url: form.action || window.location.href,
            method: form.method || 'POST',
            data: formObject,
            timestamp: Date.now(),
            priority: this.getFormPriority(form),
            csrfToken: formData.get('csrfmiddlewaretoken'),
            formId: form.id || `form_${Date.now()}`
        };

        try {
            await this.storeData('formQueue', submission);
            this.showOfflineNotification('Form saved - will submit when online', 'info');

            // If it's a draft/note form, also save to drafts
            if (this.isDraftForm(form)) {
                await this.saveDraft(formObject, form);
            }

        } catch (error) {
            console.error('[Offline Storage] Failed to queue form submission:', error);
            this.showOfflineNotification('Failed to save form offline', 'error');
        }
    }

    async handleOfflineHTMXRequest(event) {
        const url = event.detail.requestConfig.path;
        const method = event.detail.requestConfig.verb;

        // Check if we have cached data for this request
        if (method === 'GET') {
            const cachedResponse = await this.getCachedResponse(url);
            if (cachedResponse) {
                event.preventDefault();

                // Simulate HTMX response
                const target = event.detail.target;
                if (target) {
                    target.innerHTML = cachedResponse.content;
                    this.showOfflineNotification('Showing cached content', 'info');
                }
                return;
            }
        }

        // For non-GET requests, prevent and queue
        event.preventDefault();

        const requestData = {
            url: url,
            method: method,
            headers: event.detail.requestConfig.headers || {},
            timestamp: Date.now(),
            priority: 'normal',
            isHTMX: true
        };

        try {
            await this.storeData('formQueue', requestData);
            this.showOfflineNotification('Request queued for when online', 'info');
        } catch (error) {
            console.error('[Offline Storage] Failed to queue HTMX request:', error);
        }
    }

    // Draft management
    async saveDraft(data, form) {
        const draft = {
            type: this.getDraftType(form),
            data: data,
            formId: form.id,
            lastModified: Date.now(),
            autoSaved: true
        };

        try {
            await this.storeData('drafts', draft);
            this.showDraftSavedIndicator();
        } catch (error) {
            console.error('[Offline Storage] Failed to save draft:', error);
        }
    }

    async getDrafts(type = null) {
        try {
            if (type) {
                return await this.getAllData('drafts', 'type', type);
            }
            return await this.getAllData('drafts');
        } catch (error) {
            console.error('[Offline Storage] Failed to get drafts:', error);
            return [];
        }
    }

    async deleteDraft(draftId) {
        try {
            await this.deleteData('drafts', draftId);
        } catch (error) {
            console.error('[Offline Storage] Failed to delete draft:', error);
        }
    }

    // Synchronization
    async syncOfflineData() {
        if (!this.isOnline) {
            console.log('[Offline Storage] Cannot sync - still offline');
            return;
        }

        try {
            // Sync form submissions
            await this.syncFormSubmissions();

            // Sync drafts that need to be submitted
            await this.syncDrafts();

            // Clean up old offline data
            await this.cleanupOldData();

            this.showOfflineNotification('Offline data synchronized', 'success');

        } catch (error) {
            console.error('[Offline Storage] Sync failed:', error);
            this.showOfflineNotification('Sync failed - will retry later', 'warning');
        }
    }

    async syncFormSubmissions() {
        const submissions = await this.getAllData('formQueue');

        for (const submission of submissions) {
            try {
                const response = await this.submitForm(submission);

                if (response.ok) {
                    await this.deleteData('formQueue', submission.id);
                    console.log('[Offline Storage] Synced form submission:', submission.formId);
                } else {
                    console.warn('[Offline Storage] Form submission failed:', submission.formId);
                }

            } catch (error) {
                console.error('[Offline Storage] Error syncing form:', error);
            }
        }
    }

    async submitForm(submission) {
        const formData = new FormData();

        // Add form data
        for (const [key, value] of Object.entries(submission.data)) {
            formData.append(key, value);
        }

        // Add CSRF token if available
        if (submission.csrfToken) {
            formData.append('csrfmiddlewaretoken', submission.csrfToken);
        }

        return fetch(submission.url, {
            method: submission.method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
    }

    // Utility methods
    getFormPriority(form) {
        if (form.classList.contains('priority-high')) return 'high';
        if (form.classList.contains('priority-low')) return 'low';
        return 'normal';
    }

    isDraftForm(form) {
        return form.classList.contains('draft-form') ||
               form.querySelector('[name*="draft"]') !== null;
    }

    getDraftType(form) {
        if (form.classList.contains('note-form')) return 'note';
        if (form.classList.contains('project-form')) return 'project';
        if (form.classList.contains('message-form')) return 'message';
        return 'general';
    }

    async getCachedResponse(url) {
        try {
            const metadata = await this.getData('cacheMetadata', url);
            if (metadata && metadata.expiry > Date.now()) {
                return metadata;
            }
        } catch (error) {
            // No cached response
        }
        return null;
    }

    async cleanupOldData() {
        const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days

        // Clean up old drafts
        const drafts = await this.getAllData('drafts');
        for (const draft of drafts) {
            if (draft.lastModified < cutoffTime && draft.autoSaved) {
                await this.deleteData('drafts', draft.id);
            }
        }

        // Clean up expired cache metadata
        const metadata = await this.getAllData('cacheMetadata');
        for (const meta of metadata) {
            if (meta.expiry < Date.now()) {
                await this.deleteData('cacheMetadata', meta.key);
            }
        }
    }

    startPeriodicSync() {
        // Try to sync every 30 seconds when online
        setInterval(() => {
            if (this.isOnline) {
                this.syncOfflineData();
            }
        }, 30000);
    }

    // UI feedback methods
    showOfflineNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show offline-notification`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1060;
            max-width: 300px;
            animation: slideInRight 0.3s ease-out;
        `;

        notification.innerHTML = `
            <i class="fas fa-wifi-slash me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    showDraftSavedIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'draft-saved-indicator';
        indicator.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            z-index: 1050;
            animation: fadeInOut 3s ease-in-out;
        `;

        indicator.innerHTML = '<i class="fas fa-save me-2"></i>Draft saved';
        document.body.appendChild(indicator);

        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.remove();
            }
        }, 3000);
    }

    // Public API
    async getStorageStats() {
        const stats = {};
        const stores = ['projects', 'documents', 'infrastructure', 'messages', 'formQueue', 'drafts'];

        for (const store of stores) {
            try {
                const data = await this.getAllData(store);
                stats[store] = data.length;
            } catch (error) {
                stats[store] = 0;
            }
        }

        return stats;
    }

    async clearAllOfflineData() {
        const stores = ['projects', 'documents', 'infrastructure', 'messages', 'formQueue', 'drafts', 'cacheMetadata'];

        for (const storeName of stores) {
            try {
                const data = await this.getAllData(storeName);
                for (const item of data) {
                    await this.deleteData(storeName, item.id || item.key);
                }
            } catch (error) {
                console.error(`[Offline Storage] Failed to clear ${storeName}:`, error);
            }
        }

        this.showOfflineNotification('All offline data cleared', 'info');
    }
}

// Initialize offline storage manager
document.addEventListener('DOMContentLoaded', () => {
    window.offlineStorage = new OfflineStorageManager();
    console.log('[Offline Storage] Manager initialized');
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OfflineStorageManager;
}
