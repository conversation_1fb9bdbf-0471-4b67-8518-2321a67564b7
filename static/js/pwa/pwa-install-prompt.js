/**
 * PWA Installation Prompt Manager
 * Handles PWA installation prompts and user experience for CLEAR Infrastructure Platform
 */

class PWAInstallManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.installButton = null;
        this.installBanner = null;
        this.updateAvailable = false;

        this.init();
    }

    init() {
        this.checkInstallationStatus();
        this.setupEventListeners();
        this.createInstallUI();
        this.registerServiceWorker();
    }

    checkInstallationStatus() {
        // Check if app is already installed
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('[PWA] App is running in standalone mode');
        }

        // Check if service worker is supported
        if (!('serviceWorker' in navigator)) {
            console.warn('[PWA] Service Worker not supported');
            return;
        }

        // Check if app can be installed
        if ('BeforeInstallPromptEvent' in window) {
            console.log('[PWA] PWA installation supported');
        }
    }

    setupEventListeners() {
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('[PWA] Before install prompt triggered');

            // Prevent the mini-infobar from appearing on mobile
            e.preventDefault();

            // Save the event for later use
            this.deferredPrompt = e;

            // Show custom install prompt
            this.showInstallPrompt();
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', (e) => {
            console.log('[PWA] App was installed');
            this.isInstalled = true;
            this.hideInstallPrompt();
            this.showInstallSuccessMessage();
        });

        // Listen for service worker updates
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'SW_ACTIVATED') {
                    console.log('[PWA] Service Worker activated:', event.data.version);
                    this.showUpdateNotification();
                }
            });
        }

        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.showConnectionStatus('online');
        });

        window.addEventListener('offline', () => {
            this.showConnectionStatus('offline');
        });
    }

    createInstallUI() {
        // Create install banner
        this.installBanner = document.createElement('div');
        this.installBanner.id = 'pwa-install-banner';
        this.installBanner.className = 'pwa-install-banner d-none';
        this.installBanner.innerHTML = `
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <img src="/static/images/icons/icon-72x72.png" alt="CLEAR" class="pwa-banner-icon">
                    </div>
                    <div class="col">
                        <div class="pwa-banner-content">
                            <h6 class="mb-1">Install CLEAR App</h6>
                            <small class="text-muted">Get faster access and work offline</small>
                        </div>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary btn-sm me-2" id="pwa-install-btn">
                            <i class="fas fa-download me-1"></i>Install
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" id="pwa-dismiss-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add banner to page
        document.body.insertBefore(this.installBanner, document.body.firstChild);

        // Setup button event listeners
        this.installButton = document.getElementById('pwa-install-btn');
        const dismissButton = document.getElementById('pwa-dismiss-btn');

        if (this.installButton) {
            this.installButton.addEventListener('click', () => this.installApp());
        }

        if (dismissButton) {
            dismissButton.addEventListener('click', () => this.dismissInstallPrompt());
        }

        // Create floating install button for mobile
        this.createFloatingInstallButton();

        // Add PWA styles
        this.addPWAStyles();
    }

    createFloatingInstallButton() {
        const floatingButton = document.createElement('div');
        floatingButton.id = 'pwa-floating-install';
        floatingButton.className = 'pwa-floating-install d-none';
        floatingButton.innerHTML = `
            <button class="btn btn-primary rounded-circle" title="Install CLEAR App">
                <i class="fas fa-download"></i>
            </button>
        `;

        document.body.appendChild(floatingButton);

        floatingButton.addEventListener('click', () => this.installApp());
    }

    addPWAStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .pwa-install-banner {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #8CC63F, #7AB82F);
                color: white;
                padding: 12px 0;
                z-index: 1050;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                animation: slideDown 0.3s ease-out;
            }

            .pwa-banner-icon {
                width: 40px;
                height: 40px;
                border-radius: 8px;
            }

            .pwa-banner-content h6 {
                color: white;
                margin: 0;
                font-weight: 600;
            }

            .pwa-floating-install {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1040;
                animation: bounceIn 0.5s ease-out;
            }

            .pwa-floating-install .btn {
                width: 56px;
                height: 56px;
                box-shadow: 0 4px 12px rgba(140, 198, 63, 0.4);
            }

            .pwa-status-toast {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1060;
                min-width: 300px;
                animation: slideInRight 0.3s ease-out;
            }

            .pwa-offline-indicator {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 1070;
                text-align: center;
            }

            @keyframes slideDown {
                from { transform: translateY(-100%); }
                to { transform: translateY(0); }
            }

            @keyframes bounceIn {
                0% { transform: scale(0); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }

            @keyframes slideInRight {
                from { transform: translateX(100%); }
                to { transform: translateX(0); }
            }

            @media (max-width: 768px) {
                .pwa-install-banner .container-fluid {
                    padding: 0 15px;
                }

                .pwa-banner-content h6 {
                    font-size: 14px;
                }

                .pwa-banner-content small {
                    font-size: 12px;
                }
            }
        `;

        document.head.appendChild(style);
    }

    showInstallPrompt() {
        if (this.isInstalled) return;

        // Check if user has dismissed the prompt recently
        const dismissedTime = localStorage.getItem('pwa-install-dismissed');
        if (dismissedTime) {
            const daysSinceDismissed = (Date.now() - parseInt(dismissedTime)) / (1000 * 60 * 60 * 24);
            if (daysSinceDismissed < 7) {
                console.log('[PWA] Install prompt dismissed recently, not showing');
                return;
            }
        }

        // Show banner on desktop, floating button on mobile
        if (window.innerWidth > 768) {
            this.installBanner.classList.remove('d-none');
        } else {
            document.getElementById('pwa-floating-install').classList.remove('d-none');
        }

        // Auto-hide after 10 seconds
        setTimeout(() => {
            this.hideInstallPrompt();
        }, 10000);
    }

    hideInstallPrompt() {
        this.installBanner.classList.add('d-none');
        document.getElementById('pwa-floating-install').classList.add('d-none');
    }

    dismissInstallPrompt() {
        this.hideInstallPrompt();
        localStorage.setItem('pwa-install-dismissed', Date.now().toString());
    }

    async installApp() {
        if (!this.deferredPrompt) {
            console.log('[PWA] No deferred prompt available');
            this.showManualInstallInstructions();
            return;
        }

        try {
            // Show the install prompt
            this.deferredPrompt.prompt();

            // Wait for the user to respond
            const { outcome } = await this.deferredPrompt.userChoice;

            console.log('[PWA] User choice:', outcome);

            if (outcome === 'accepted') {
                console.log('[PWA] User accepted the install prompt');
            } else {
                console.log('[PWA] User dismissed the install prompt');
            }

            // Clear the deferred prompt
            this.deferredPrompt = null;
            this.hideInstallPrompt();

        } catch (error) {
            console.error('[PWA] Error during installation:', error);
            this.showInstallError();
        }
    }

    showManualInstallInstructions() {
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/.test(navigator.userAgent);

        let instructions = '';

        if (isIOS) {
            instructions = `
                <div class="alert alert-info">
                    <h6><i class="fab fa-apple me-2"></i>Install on iOS</h6>
                    <ol class="mb-0">
                        <li>Tap the Share button <i class="fas fa-share"></i></li>
                        <li>Scroll down and tap "Add to Home Screen"</li>
                        <li>Tap "Add" to install the app</li>
                    </ol>
                </div>
            `;
        } else if (isAndroid) {
            instructions = `
                <div class="alert alert-info">
                    <h6><i class="fab fa-android me-2"></i>Install on Android</h6>
                    <ol class="mb-0">
                        <li>Tap the menu button (⋮)</li>
                        <li>Select "Add to Home screen" or "Install app"</li>
                        <li>Tap "Add" to install</li>
                    </ol>
                </div>
            `;
        } else {
            instructions = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-desktop me-2"></i>Install on Desktop</h6>
                    <p class="mb-0">Look for the install icon in your browser's address bar, or check the browser menu for "Install CLEAR" option.</p>
                </div>
            `;
        }

        this.showToast('Install Instructions', instructions, 'info', 8000);
    }

    showInstallSuccessMessage() {
        this.showToast(
            'App Installed!',
            '<p class="mb-0"><i class="fas fa-check-circle text-success me-2"></i>CLEAR has been installed successfully. You can now access it from your home screen.</p>',
            'success',
            5000
        );
    }

    showInstallError() {
        this.showToast(
            'Installation Error',
            '<p class="mb-0"><i class="fas fa-exclamation-triangle text-warning me-2"></i>There was an error installing the app. Please try again later.</p>',
            'warning',
            5000
        );
    }

    showUpdateNotification() {
        if (this.updateAvailable) return;

        this.updateAvailable = true;

        const updateToast = `
            <div class="d-flex align-items-center">
                <i class="fas fa-sync-alt text-primary me-2"></i>
                <div class="flex-grow-1">
                    <strong>Update Available</strong>
                    <div class="small">A new version of CLEAR is ready.</div>
                </div>
                <button class="btn btn-sm btn-primary ms-2" onclick="pwaManager.applyUpdate()">
                    Update
                </button>
            </div>
        `;

        this.showToast('', updateToast, 'info', 0); // Don't auto-hide
    }

    applyUpdate() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                if (registration.waiting) {
                    registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                    window.location.reload();
                }
            });
        }
    }

    showConnectionStatus(status) {
        const isOnline = status === 'online';
        const message = isOnline ?
            '<i class="fas fa-wifi text-success me-2"></i>Back online! All features are available.' :
            '<i class="fas fa-wifi-slash text-warning me-2"></i>You\'re offline. Some features may be limited.';

        this.showToast(
            isOnline ? 'Connected' : 'Offline',
            `<p class="mb-0">${message}</p>`,
            isOnline ? 'success' : 'warning',
            3000
        );
    }

    showToast(title, content, type = 'info', autoHide = 5000) {
        const toastId = 'pwa-toast-' + Date.now();
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast pwa-status-toast`;
        toast.setAttribute('role', 'alert');

        const bgClass = {
            'success': 'bg-success',
            'warning': 'bg-warning',
            'error': 'bg-danger',
            'info': 'bg-info'
        }[type] || 'bg-info';

        toast.innerHTML = `
            <div class="toast-header ${bgClass} text-white">
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${content}
            </div>
        `;

        document.body.appendChild(toast);

        // Initialize Bootstrap toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: autoHide > 0,
            delay: autoHide
        });

        bsToast.show();

        // Remove from DOM after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    async registerServiceWorker() {
        if (!('serviceWorker' in navigator)) {
            console.warn('[PWA] Service Worker not supported');
            return;
        }

        try {
            const registration = await navigator.serviceWorker.register('/static/js/workers/pwa-service-worker.js', {
                scope: '/'
            });

            console.log('[PWA] Service Worker registered successfully:', registration);

            // Check for updates
            registration.addEventListener('updatefound', () => {
                console.log('[PWA] Service Worker update found');
                const newWorker = registration.installing;

                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        console.log('[PWA] New Service Worker installed');
                        this.showUpdateNotification();
                    }
                });
            });

        } catch (error) {
            console.error('[PWA] Service Worker registration failed:', error);
        }
    }

    // Public API methods
    getInstallationStatus() {
        return {
            isInstalled: this.isInstalled,
            canInstall: !!this.deferredPrompt,
            isOnline: navigator.onLine
        };
    }

    forceShowInstallPrompt() {
        this.showInstallPrompt();
    }

    clearCache() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                const messageChannel = new MessageChannel();
                messageChannel.port1.onmessage = (event) => {
                    if (event.data.success) {
                        console.log('[PWA] Cache cleared successfully');
                        this.showToast('Cache Cleared', 'App cache has been cleared successfully.', 'success');
                    }
                };

                registration.active.postMessage(
                    { type: 'CLEAR_CACHE' },
                    [messageChannel.port2]
                );
            });
        }
    }
}

// Initialize PWA manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAInstallManager();
    console.log('[PWA] PWA Install Manager initialized');
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAInstallManager;
}
