/**
 * CLEAR PWA Service Worker
 * Comprehensive Progressive Web App functionality for the CLEAR Infrastructure Platform
 * Implements caching strategies, offline functionality, and background sync
 */

const CACHE_VERSION = 'clear-pwa-v1.0.0';
const STATIC_CACHE = `${CACHE_VERSION}-static`;
const DYNAMIC_CACHE = `${CACHE_VERSION}-dynamic`;
const OFFLINE_CACHE = `${CACHE_VERSION}-offline`;

// Cache configuration
const CACHE_CONFIG = {
  static: {
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    maxEntries: 100
  },
  dynamic: {
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    maxEntries: 200
  },
  offline: {
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    maxEntries: 50
  }
};

// Critical app shell resources to cache immediately
const APP_SHELL_RESOURCES = [
  '/',
  '/static/css/core/style.css',
  '/static/css/core/bootstrap-egis-theme.css',
  '/static/css/bundles/critical.min.css',
  '/static/js/htmx/htmx.min.js',
  '/static/js/htmx/htmx-error-handler.js',
  '/static/js/htmx/htmx-loading-states.js',
  '/static/js/bootstrap/bootstrap.bundle.min.js',
  '/static/images/icons/icon-192x192.png',
  '/static/images/icons/icon-512x512.png',
  '/offline/',
  '/manifest.json'
];

// Routes that should work offline
const OFFLINE_ROUTES = [
  '/',
  '/dashboard/',
  '/projects/',
  '/infrastructure/map/',
  '/messaging/',
  '/offline/'
];

// Routes that require network (no offline fallback)
const NETWORK_ONLY_ROUTES = [
  '/api/',
  '/admin/',
  '/auth/',
  '/htmx/'
];

// Install event - cache app shell
self.addEventListener('install', event => {
  console.log('[PWA SW] Installing service worker...');

  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then(cache => {
        console.log('[PWA SW] Caching app shell resources');
        return cache.addAll(APP_SHELL_RESOURCES.map(url => new Request(url, {
          cache: 'reload'
        })));
      }),
      caches.open(OFFLINE_CACHE).then(cache => {
        console.log('[PWA SW] Caching offline resources');
        return cache.add('/offline/');
      })
    ]).then(() => {
      console.log('[PWA SW] Installation complete');
      return self.skipWaiting();
    }).catch(error => {
      console.error('[PWA SW] Installation failed:', error);
    })
  );
});

// Activate event - clean old caches and claim clients
self.addEventListener('activate', event => {
  console.log('[PWA SW] Activating service worker...');

  event.waitUntil(
    Promise.all([
      // Clean old caches
      caches.keys().then(cacheNames => {
        const deletePromises = cacheNames
          .filter(name => name.startsWith('clear-pwa-') && name !== CACHE_VERSION)
          .map(name => {
            console.log('[PWA SW] Deleting old cache:', name);
            return caches.delete(name);
          });
        return Promise.all(deletePromises);
      }),
      // Claim all clients
      self.clients.claim()
    ]).then(() => {
      console.log('[PWA SW] Activation complete');
      // Notify clients about update
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'SW_ACTIVATED',
            version: CACHE_VERSION
          });
        });
      });
    })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
  const request = event.request;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  event.respondWith(handleRequest(request));
});

async function handleRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;

  try {
    // Network-only routes
    if (NETWORK_ONLY_ROUTES.some(route => pathname.startsWith(route))) {
      return await handleNetworkOnly(request);
    }

    // Static assets - cache first
    if (pathname.startsWith('/static/')) {
      return await handleStaticAssets(request);
    }

    // API endpoints - network first with cache fallback
    if (pathname.startsWith('/api/')) {
      return await handleApiRequests(request);
    }

    // HTMX requests - network first with intelligent fallback
    if (request.headers.get('HX-Request')) {
      return await handleHTMXRequests(request);
    }

    // HTML pages - stale while revalidate
    if (request.headers.get('Accept')?.includes('text/html')) {
      return await handleHTMLPages(request);
    }

    // Default: network first
    return await handleNetworkFirst(request);

  } catch (error) {
    console.error('[PWA SW] Request handling error:', error);
    return await handleOfflineFallback(request);
  }
}

async function handleStaticAssets(request) {
  const cache = await caches.open(STATIC_CACHE);
  let response = await cache.match(request);

  if (response) {
    // Check if cache entry is fresh
    const cachedTime = response.headers.get('sw-cached-time');
    if (cachedTime && Date.now() - parseInt(cachedTime) < CACHE_CONFIG.static.maxAge) {
      return response;
    }
  }

  try {
    // Fetch from network
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Clone and add timestamp header
      const responseToCache = networkResponse.clone();
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-time', Date.now().toString());

      const cachedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });

      // Cache the response
      cache.put(request, cachedResponse);
      return networkResponse;
    }
  } catch (error) {
    console.log('[PWA SW] Network failed for static asset, using cache:', request.url);
  }

  // Return cached version if available
  return response || new Response('Asset not available offline', { status: 404 });
}

async function handleHTMLPages(request) {
  const cache = await caches.open(DYNAMIC_CACHE);
  const pathname = new URL(request.url).pathname;

  try {
    // Try network first
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful responses for offline routes
      if (OFFLINE_ROUTES.some(route => pathname === route || pathname.startsWith(route))) {
        const responseToCache = networkResponse.clone();
        const headers = new Headers(responseToCache.headers);
        headers.set('sw-cached-time', Date.now().toString());

        const cachedResponse = new Response(responseToCache.body, {
          status: responseToCache.status,
          statusText: responseToCache.statusText,
          headers: headers
        });

        cache.put(request, cachedResponse);
      }

      return networkResponse;
    }
  } catch (error) {
    console.log('[PWA SW] Network failed for HTML page:', request.url);
  }

  // Try cache fallback
  const cachedResponse = await cache.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  // Return offline page for supported routes
  if (OFFLINE_ROUTES.some(route => pathname === route || pathname.startsWith(route))) {
    return await handleOfflinePage();
  }

  return new Response('Page not available offline', {
    status: 404,
    headers: { 'Content-Type': 'text/html' }
  });
}

async function handleHTMXRequests(request) {
  const cache = await caches.open(DYNAMIC_CACHE);

  try {
    // Network first for HTMX requests
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful HTMX responses
      const responseToCache = networkResponse.clone();
      cache.put(request, responseToCache);
      return networkResponse;
    }
  } catch (error) {
    console.log('[PWA SW] Network failed for HTMX request:', request.url);
  }

  // Try cache fallback
  const cachedResponse = await cache.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  // Return offline HTMX response
  return new Response(
    '<div class="alert alert-warning">This content is not available offline. Please check your connection.</div>',
    {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
        'HX-Trigger': 'offline-content'
      }
    }
  );
}

async function handleApiRequests(request) {
  try {
    // Network first for API requests
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful GET API responses
      if (request.method === 'GET') {
        const cache = await caches.open(DYNAMIC_CACHE);
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    }
  } catch (error) {
    console.log('[PWA SW] Network failed for API request:', request.url);
  }

  // Try cache fallback for GET requests
  if (request.method === 'GET') {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
  }

  // Return offline API response
  return new Response(
    JSON.stringify({
      error: 'API not available offline',
      offline: true,
      message: 'This request requires an internet connection.'
    }),
    {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}

async function handleNetworkOnly(request) {
  return await fetch(request);
}

async function handleNetworkFirst(request) {
  const cache = await caches.open(DYNAMIC_CACHE);

  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }
  } catch (error) {
    console.log('[PWA SW] Network failed, trying cache:', request.url);
  }

  const cachedResponse = await cache.match(request);
  return cachedResponse || new Response('Content not available offline', { status: 404 });
}

async function handleOfflinePage() {
  const cache = await caches.open(OFFLINE_CACHE);
  const offlineResponse = await cache.match('/offline/');

  if (offlineResponse) {
    return offlineResponse;
  }

  // Fallback offline HTML
  return new Response(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Offline - CLEAR</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .offline-container { max-width: 500px; margin: 0 auto; }
            .offline-icon { font-size: 64px; margin-bottom: 20px; }
            h1 { color: #8CC63F; }
            .retry-btn {
                background: #8CC63F;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div class="offline-container">
            <div class="offline-icon">📡</div>
            <h1>You're Offline</h1>
            <p>It looks like you've lost your internet connection. Some features may not be available.</p>
            <button class="retry-btn" onclick="window.location.reload()">Try Again</button>
        </div>
    </body>
    </html>
  `, {
    status: 200,
    headers: { 'Content-Type': 'text/html' }
  });
}

async function handleOfflineFallback(request) {
  const url = new URL(request.url);

  // For HTML requests, return offline page
  if (request.headers.get('Accept')?.includes('text/html')) {
    return await handleOfflinePage();
  }

  // For other requests, return appropriate offline response
  return new Response('Content not available offline', {
    status: 503,
    statusText: 'Service Unavailable'
  });
}

// Background sync for form submissions
self.addEventListener('sync', event => {
  console.log('[PWA SW] Background sync triggered:', event.tag);

  if (event.tag === 'background-sync-forms') {
    event.waitUntil(syncFormSubmissions());
  }
});

async function syncFormSubmissions() {
  // Implementation for syncing offline form submissions
  console.log('[PWA SW] Syncing offline form submissions...');

  // This would typically involve:
  // 1. Retrieving stored form data from IndexedDB
  // 2. Attempting to submit to server
  // 3. Removing successfully synced data
  // 4. Notifying user of sync status
}

// Push notification handling
self.addEventListener('push', event => {
  console.log('[PWA SW] Push notification received');

  const options = {
    body: 'You have new updates in CLEAR',
    icon: '/static/images/icons/icon-192x192.png',
    badge: '/static/images/icons/icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Updates',
        icon: '/static/images/icons/shortcut-dashboard.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/static/images/icons/icon-72x72.png'
      }
    ]
  };

  if (event.data) {
    const payload = event.data.json();
    options.body = payload.body || options.body;
    options.title = payload.title || 'CLEAR Notification';
  }

  event.waitUntil(
    self.registration.showNotification('CLEAR Infrastructure Platform', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  console.log('[PWA SW] Notification clicked:', event.action);

  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/dashboard/')
    );
  } else if (event.action === 'close') {
    // Just close the notification
    return;
  } else {
    // Default action - open app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Message handling from main thread
self.addEventListener('message', event => {
  console.log('[PWA SW] Message received:', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({
      type: 'VERSION',
      version: CACHE_VERSION
    });
  }

  if (event.data && event.data.type === 'CLEAR_CACHE') {
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }).then(() => {
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED',
          success: true
        });
      })
    );
  }
});

console.log('[PWA SW] CLEAR PWA Service Worker loaded successfully');
