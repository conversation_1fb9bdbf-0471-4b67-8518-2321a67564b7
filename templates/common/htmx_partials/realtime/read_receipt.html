<!-- Read Receipt Partial -->
<!-- Replaces: JavaScript read receipt DOM updates -->
{% comment %}
Usage:
  {% include 'common/htmx_partials/realtime/read_receipt.html' with message=message %}

Context Variables:
  - message: ChatMessage instance with read_status and users who read it
  - compact: Boolean for compact display mode
{% endcomment %}

{% if message.read_status.exists %}
  <div class="read-receipt d-flex align-items-center text-muted small mt-1"
       data-message-id="{{ message.id }}"
       data-read-count="{{ message.read_status.count }}">

    {% with read_count=message.read_status.count %}

      {% if read_count == 1 %}
        <i class="bi bi-check2 me-1" style="font-size: 0.875rem;"></i>
        <span>Read by {{ message.read_status.first.user.get_display_name }}</span>
      {% elif read_count == 2 %}
        <i class="bi bi-check2-all me-1 text-primary"
           style="font-size: 0.875rem"></i>
        <span>Read by {{ message.read_status.all.0.user.get_display_name }} and {{ message.read_status.all.1.user.get_display_name }}</span>
      {% elif read_count > 2 %}
        <i class="bi bi-check2-all me-1 text-primary"
           style="font-size: 0.875rem"></i>
        <span>Read by {{ message.read_status.first.user.get_display_name }} and {{ read_count|add:"-1" }} others</span>
      {% endif %}

    {% endwith %}

    <!-- Timestamp of most recent read -->

    {% if message.read_status.exists %}

      {% with latest_read=message.read_status.latest %}
        <span class="ms-2 opacity-75" style="font-size: 0.75rem;">{{ latest_read.read_at|timesince }} ago</span>
      {% endwith %}

    {% endif %}

  </div>
  <!-- CSS for read receipt styling -->
  <style>
  .read-receipt {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
    padding-left: 0.5rem;
  }
  
  .read-receipt .bi-check2 {
    color: #28a745;
  }
  
  .read-receipt .bi-check2-all {
    color: #007bff;
  }
  
  .read-receipt:empty {
    display: none;
  }
  
  /* Animation for when read receipts update */
  .read-receipt {
    transition: all 0.3s ease;
  }
  
  .read-receipt.updated {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 0.25rem;
    padding: 0.125rem 0.25rem;
    animation: fadeInOut 2s ease-in-out;
  }
  
  @keyframes fadeInOut {
    0% { background-color: transparent; }
    50% { background-color: rgba(0, 123, 255, 0.1); }
    100% { background-color: transparent; }
  }
  </style>
{% else %}
  <!-- Empty state when no one has read the message -->
  <div class="read-receipt-empty" style="display: none;">
    <!-- Hidden placeholder for HTMX swapping -->
  </div>
{% endif %}
