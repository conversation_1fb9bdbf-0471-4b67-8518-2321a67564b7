/**
 * Collaboration Page Object Model
 * Manages real-time collaborative features, document editing, and multi-user interactions
 */

const { BasePage } = require('../BasePage');

class CollaborationPage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      ...this.selectors,
      
      // Collaborative editor
      editor: '[data-testid="collaborative-editor"], .collaborative-editor',
      editorContent: '[data-testid="editor-content"], .editor-content',
      editorToolbar: '[data-testid="editor-toolbar"], .editor-toolbar',
      saveButton: '[data-testid="save-document"], .save-btn',
      autoSaveIndicator: '[data-testid="autosave-status"], .autosave-indicator',
      
      // Cursor and selection tracking
      userCursor: '[data-testid="user-cursor"], .user-cursor',
      userSelection: '[data-testid="user-selection"], .user-selection',
      cursorLabel: '[data-testid="cursor-label"], .cursor-label',
      
      // Collaborative participants
      participantsList: '[data-testid="participants-list"], .participants-list',
      participantItem: '[data-testid="participant"], .participant-item',
      participantName: '[data-testid="participant-name"], .participant-name',
      participantStatus: '[data-testid="participant-status"], .participant-status',
      participantCursor: '[data-testid="participant-cursor"], .participant-cursor-color',
      
      // Document operations
      operationLog: '[data-testid="operation-log"], .operation-log',
      operationItem: '[data-testid="operation"], .operation-item',
      conflictIndicator: '[data-testid="conflict-indicator"], .conflict-indicator',
      conflictResolver: '[data-testid="conflict-resolver"], .conflict-resolver',
      
      // Version history
      versionHistory: '[data-testid="version-history"], .version-history',
      versionItem: '[data-testid="version"], .version-item',
      versionTimestamp: '[data-testid="version-timestamp"], .version-timestamp',
      versionAuthor: '[data-testid="version-author"], .version-author',
      restoreVersionBtn: '[data-testid="restore-version"], .restore-version-btn',
      
      // Comments and annotations
      commentButton: '[data-testid="add-comment"], .add-comment-btn',
      commentThread: '[data-testid="comment-thread"], .comment-thread',
      commentItem: '[data-testid="comment"], .comment-item',
      commentInput: '[data-testid="comment-input"], .comment-input',
      replyButton: '[data-testid="reply-comment"], .reply-btn',
      
      // Real-time synchronization
      syncStatus: '[data-testid="sync-status"], .sync-status',
      syncIndicator: '[data-testid="sync-indicator"], .sync-indicator',
      connectionQuality: '[data-testid="connection-quality"], .connection-quality',
      latencyIndicator: '[data-testid="latency"], .latency-indicator',
      
      // Document sharing
      shareButton: '[data-testid="share-document"], .share-btn',
      shareModal: '[data-testid="share-modal"], .share-modal',
      shareLink: '[data-testid="share-link"], .share-link',
      permissionSelect: '[data-testid="permission-select"], .permission-select',
      inviteUserInput: '[data-testid="invite-user"], .invite-user-input',
      
      // Presence indicators
      typingIndicator: '[data-testid="typing-indicator"], .typing-indicator',
      editingIndicator: '[data-testid="editing-indicator"], .editing-indicator',
      viewingIndicator: '[data-testid="viewing-indicator"], .viewing-indicator',
      
      // Document settings
      documentTitle: '[data-testid="document-title"], .document-title',
      documentSettings: '[data-testid="document-settings"], .document-settings',
      accessLevel: '[data-testid="access-level"], .access-level',
      
      // Collaboration tools
      chatToggle: '[data-testid="toggle-chat"], .chat-toggle',
      sidebarChat: '[data-testid="sidebar-chat"], .sidebar-chat',
      videoCallBtn: '[data-testid="start-video-call"], .video-call-btn',
      screenShareBtn: '[data-testid="screen-share"], .screen-share-btn',
      
      // Change tracking
      changeHighlight: '[data-testid="change-highlight"], .change-highlight',
      acceptChangeBtn: '[data-testid="accept-change"], .accept-change-btn',
      rejectChangeBtn: '[data-testid="reject-change"], .reject-change-btn',
      trackChangesToggle: '[data-testid="track-changes"], .track-changes-toggle'
    };
  }

  // Navigation methods
  async navigateToDocument(documentId) {
    await this.goto(`/realtime/documents/${documentId}/edit/`);
    await this.waitForElement(this.selectors.editor);
    await this.waitForConnection();
  }

  async createNewDocument(title = 'New Collaborative Document') {
    await this.goto('/realtime/documents/create/');
    await this.waitForElement('.document-create-form, [data-testid="document-create-form"]');
    
    await this.fillField('title', title);
    await this.submitForm('.document-create-form, [data-testid="document-create-form"]');
    await this.htmx.waitForHTMXRequest();
    
    // Extract document ID from URL
    const url = await this.page.url();
    const match = url.match(/documents\/([a-f0-9-]+)/);
    return match ? match[1] : null;
  }

  // Editor operations
  async getEditorContent() {
    const content = await this.page.textContent(this.selectors.editorContent);
    return content ? content.trim() : '';
  }

  async setEditorContent(content) {
    await this.page.fill(this.selectors.editorContent, content);
    await this.waitForSyncIndicator();
  }

  async insertTextAtPosition(text, position) {
    await this.page.evaluate(
      ({ content, pos }) => {
        const editor = document.querySelector('[data-testid="editor-content"], .editor-content');
        if (editor) {
          const currentText = editor.textContent || '';
          const newText = currentText.slice(0, pos) + content + currentText.slice(pos);
          editor.textContent = newText;
          
          // Trigger input event for real-time sync
          editor.dispatchEvent(new Event('input', { bubbles: true }));
        }
      },
      { content: text, pos: position }
    );
    
    await this.waitForSyncIndicator();
  }

  async deleteTextRange(start, end) {
    await this.page.evaluate(
      ({ startPos, endPos }) => {
        const editor = document.querySelector('[data-testid="editor-content"], .editor-content');
        if (editor) {
          const currentText = editor.textContent || '';
          const newText = currentText.slice(0, startPos) + currentText.slice(endPos);
          editor.textContent = newText;
          
          // Trigger input event for real-time sync
          editor.dispatchEvent(new Event('input', { bubbles: true }));
        }
      },
      { startPos: start, endPos: end }
    );
    
    await this.waitForSyncIndicator();
  }

  async selectTextRange(start, end) {
    await this.page.evaluate(
      ({ startPos, endPos }) => {
        const editor = document.querySelector('[data-testid="editor-content"], .editor-content');
        if (editor && window.getSelection) {
          const selection = window.getSelection();
          const range = document.createRange();
          
          const textNode = editor.firstChild || editor;
          range.setStart(textNode, Math.min(startPos, textNode.textContent?.length || 0));
          range.setEnd(textNode, Math.min(endPos, textNode.textContent?.length || 0));
          
          selection.removeAllRanges();
          selection.addRange(range);
        }
      },
      { startPos: start, endPos: end }
    );
  }

  async getCursorPosition() {
    return await this.page.evaluate(() => {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        return range.startOffset;
      }
      return 0;
    });
  }

  // Collaboration features
  async getParticipants() {
    const participants = await this.page.$$(this.selectors.participantItem);
    const participantData = [];

    for (const participant of participants) {
      const name = await participant.$(this.selectors.participantName);
      const status = await participant.$(this.selectors.participantStatus);
      const cursorColor = await participant.$(this.selectors.participantCursor);
      
      const nameText = name ? await name.textContent() : '';
      const statusText = status ? await status.textContent() : '';
      const colorValue = cursorColor ? await cursorColor.getAttribute('data-color') : '';
      const userId = await participant.getAttribute('data-user-id');
      const isActive = await participant.getAttribute('data-active') === 'true';

      participantData.push({
        id: userId,
        name: nameText.trim(),
        status: statusText.trim(),
        cursorColor: colorValue,
        isActive
      });
    }

    return participantData;
  }

  async waitForParticipantJoin(userId, timeout = 10000) {
    await this.page.waitForFunction(
      (id, selector) => {
        const participant = document.querySelector(`[data-user-id="${id}"]`);
        return participant && participant.getAttribute('data-active') === 'true';
      },
      { timeout },
      userId,
      this.selectors.participantItem
    );
  }

  async waitForParticipantLeave(userId, timeout = 10000) {
    await this.page.waitForFunction(
      (id, selector) => {
        const participant = document.querySelector(`[data-user-id="${id}"]`);
        return !participant || participant.getAttribute('data-active') === 'false';
      },
      { timeout },
      userId,
      this.selectors.participantItem
    );
  }

  async getUserCursors() {
    const cursors = await this.page.$$(this.selectors.userCursor);
    const cursorData = [];

    for (const cursor of cursors) {
      const label = await cursor.$(this.selectors.cursorLabel);
      const labelText = label ? await label.textContent() : '';
      const userId = await cursor.getAttribute('data-user-id');
      const position = await cursor.getAttribute('data-position');
      const color = await cursor.getAttribute('data-color');

      cursorData.push({
        userId,
        label: labelText.trim(),
        position: parseInt(position, 10) || 0,
        color
      });
    }

    return cursorData;
  }

  async waitForCursorUpdate(userId, timeout = 5000) {
    await this.page.waitForFunction(
      (id, selector) => {
        const cursor = document.querySelector(`[data-user-id="${id}"]${selector}`);
        return cursor && cursor.getAttribute('data-updated') === 'true';
      },
      { timeout },
      userId,
      this.selectors.userCursor
    );
  }

  // Real-time synchronization
  async waitForConnection() {
    await this.page.waitForFunction(
      (selector) => {
        const syncStatus = document.querySelector(selector);
        return syncStatus && syncStatus.getAttribute('data-connected') === 'true';
      },
      {},
      this.selectors.syncStatus
    );
  }

  async waitForSyncIndicator(timeout = 5000) {
    try {
      // Wait for sync to start
      await this.page.waitForSelector(`${this.selectors.syncIndicator}[data-syncing="true"]`, { timeout: 1000 });
      // Wait for sync to complete
      await this.page.waitForSelector(`${this.selectors.syncIndicator}[data-syncing="false"]`, { timeout });
    } catch (error) {
      // Sync might be too fast to catch
    }
  }

  async getSyncStatus() {
    const syncElement = await this.page.$(this.selectors.syncStatus);
    if (syncElement) {
      const status = await syncElement.textContent();
      const isConnected = await syncElement.getAttribute('data-connected') === 'true';
      const lastSync = await syncElement.getAttribute('data-last-sync');
      
      return {
        status: status.trim(),
        connected: isConnected,
        lastSync
      };
    }
    
    return { status: 'unknown', connected: false, lastSync: null };
  }

  async getConnectionQuality() {
    const qualityElement = await this.page.$(this.selectors.connectionQuality);
    if (qualityElement) {
      const quality = await qualityElement.textContent();
      const latencyElement = await this.page.$(this.selectors.latencyIndicator);
      const latency = latencyElement ? await latencyElement.textContent() : '0ms';
      
      return {
        quality: quality.trim(),
        latency: latency.trim()
      };
    }
    
    return { quality: 'unknown', latency: '0ms' };
  }

  // Operations and conflicts
  async getOperationHistory() {
    const operations = await this.page.$$(this.selectors.operationItem);
    const operationData = [];

    for (const operation of operations) {
      const type = await operation.getAttribute('data-operation-type');
      const author = await operation.getAttribute('data-author');
      const timestamp = await operation.getAttribute('data-timestamp');
      const content = await operation.textContent();

      operationData.push({
        type,
        author,
        timestamp,
        content: content.trim()
      });
    }

    return operationData;
  }

  async waitForConflict(timeout = 10000) {
    await this.page.waitForSelector(this.selectors.conflictIndicator, { timeout });
  }

  async resolveConflict(resolution = 'accept') {
    await this.waitForElement(this.selectors.conflictResolver);
    
    const resolveButton = resolution === 'accept' ? 
      '.accept-resolution-btn, [data-testid="accept-resolution"]' : 
      '.reject-resolution-btn, [data-testid="reject-resolution"]';
    
    await this.page.click(resolveButton);
    await this.htmx.waitForHTMXRequest();
  }

  // Comments and annotations
  async addComment(text, position = null) {
    if (position !== null) {
      await this.selectTextRange(position, position + 1);
    }
    
    await this.page.click(this.selectors.commentButton);
    await this.waitForElement(this.selectors.commentInput);
    
    await this.page.fill(this.selectors.commentInput, text);
    await this.page.click('.submit-comment-btn, [data-testid="submit-comment"]');
    await this.htmx.waitForHTMXRequest();
  }

  async getComments() {
    const comments = await this.page.$$(this.selectors.commentItem);
    const commentData = [];

    for (const comment of comments) {
      const authorElement = await comment.$('.comment-author, [data-testid="comment-author"]');
      const contentElement = await comment.$('.comment-content, [data-testid="comment-content"]');
      const timestampElement = await comment.$('.comment-timestamp, [data-testid="comment-timestamp"]');
      
      const author = authorElement ? await authorElement.textContent() : '';
      const content = contentElement ? await contentElement.textContent() : '';
      const timestamp = timestampElement ? await timestampElement.textContent() : '';
      const commentId = await comment.getAttribute('data-comment-id');
      const position = await comment.getAttribute('data-position');

      commentData.push({
        id: commentId,
        author: author.trim(),
        content: content.trim(),
        timestamp: timestamp.trim(),
        position: parseInt(position, 10) || 0
      });
    }

    return commentData;
  }

  async replyToComment(commentId, replyText) {
    await this.page.click(`[data-comment-id="${commentId}"] ${this.selectors.replyButton}`);
    await this.waitForElement(this.selectors.commentInput);
    
    await this.page.fill(this.selectors.commentInput, replyText);
    await this.page.click('.submit-reply-btn, [data-testid="submit-reply"]');
    await this.htmx.waitForHTMXRequest();
  }

  // Version history
  async getVersionHistory() {
    await this.page.click('.version-history-btn, [data-testid="version-history-button"]');
    await this.waitForElement(this.selectors.versionHistory);
    
    const versions = await this.page.$$(this.selectors.versionItem);
    const versionData = [];

    for (const version of versions) {
      const timestamp = await version.$(this.selectors.versionTimestamp);
      const author = await version.$(this.selectors.versionAuthor);
      
      const timestampText = timestamp ? await timestamp.textContent() : '';
      const authorText = author ? await author.textContent() : '';
      const versionId = await version.getAttribute('data-version-id');
      const isCurrent = await version.getAttribute('data-current') === 'true';

      versionData.push({
        id: versionId,
        timestamp: timestampText.trim(),
        author: authorText.trim(),
        isCurrent
      });
    }

    return versionData;
  }

  async restoreVersion(versionId) {
    await this.page.click(`[data-version-id="${versionId}"] ${this.selectors.restoreVersionBtn}`);
    await this.waitForModal('Restore Version');
    await this.page.click('.confirm-restore-btn, [data-testid="confirm-restore"]');
    await this.htmx.waitForHTMXRequest();
  }

  // Document sharing
  async shareDocument(users = [], permission = 'edit') {
    await this.page.click(this.selectors.shareButton);
    await this.waitForModal('Share Document');
    
    // Set permission level
    await this.selectOption('permission_level', permission);
    
    // Add users
    for (const user of users) {
      await this.page.fill(this.selectors.inviteUserInput, user);
      await this.page.click('.add-user-btn, [data-testid="add-user"]');
    }
    
    await this.page.click('.send-invites-btn, [data-testid="send-invites"]');
    await this.htmx.waitForHTMXRequest();
  }

  async getShareLink() {
    await this.page.click(this.selectors.shareButton);
    await this.waitForModal('Share Document');
    
    const linkElement = await this.page.$(this.selectors.shareLink);
    if (linkElement) {
      return await linkElement.getAttribute('value') || await linkElement.textContent();
    }
    
    return null;
  }

  // Presence indicators
  async getTypingUsers() {
    const typingText = await this.page.textContent(this.selectors.typingIndicator);
    if (typingText) {
      // Extract usernames from "User1 and User2 are typing..." format
      const matches = typingText.match(/([^,\s]+(?:\s+[^,\s]+)*?)(?:\s+and\s+|\s*,\s*|\s+are?\s+typing)/g);
      return matches ? matches.map(match => match.replace(/\s+(and|are?)\s+(typing)?/, '').trim()) : [];
    }
    return [];
  }

  async waitForTypingIndicator(username, timeout = 5000) {
    await this.page.waitForFunction(
      (selector, user) => {
        const indicator = document.querySelector(selector);
        return indicator && indicator.textContent.includes(user);
      },
      { timeout },
      this.selectors.typingIndicator,
      username
    );
  }

  async startTyping() {
    // Simulate typing by dispatching input events
    await this.page.evaluate(() => {
      const editor = document.querySelector('[data-testid="editor-content"], .editor-content');
      if (editor) {
        editor.dispatchEvent(new Event('input', { bubbles: true }));
      }
    });
  }

  async stopTyping() {
    // Stop typing by waiting for the typing timeout
    await this.page.waitForTimeout(3000); // Assuming 3 second typing timeout
  }

  // Change tracking
  async enableChangeTracking() {
    await this.page.check(this.selectors.trackChangesToggle);
    await this.htmx.waitForHTMXRequest();
  }

  async disableChangeTracking() {
    await this.page.uncheck(this.selectors.trackChangesToggle);
    await this.htmx.waitForHTMXRequest();
  }

  async getTrackedChanges() {
    const changes = await this.page.$$(this.selectors.changeHighlight);
    const changeData = [];

    for (const change of changes) {
      const type = await change.getAttribute('data-change-type');
      const author = await change.getAttribute('data-author');
      const timestamp = await change.getAttribute('data-timestamp');
      const content = await change.textContent();

      changeData.push({
        type,
        author,
        timestamp,
        content: content.trim()
      });
    }

    return changeData;
  }

  async acceptChange(changeId) {
    await this.page.click(`[data-change-id="${changeId}"] ${this.selectors.acceptChangeBtn}`);
    await this.htmx.waitForHTMXRequest();
  }

  async rejectChange(changeId) {
    await this.page.click(`[data-change-id="${changeId}"] ${this.selectors.rejectChangeBtn}`);
    await this.htmx.waitForHTMXRequest();
  }

  // Utility methods
  async saveDocument() {
    await this.page.click(this.selectors.saveButton);
    await this.htmx.waitForHTMXRequest();
    
    // Wait for save confirmation
    await this.page.waitForSelector('.save-success, [data-testid="save-success"]', { timeout: 5000 });
  }

  async getAutoSaveStatus() {
    const indicator = await this.page.$(this.selectors.autoSaveIndicator);
    if (indicator) {
      const status = await indicator.textContent();
      const lastSaved = await indicator.getAttribute('data-last-saved');
      
      return {
        status: status.trim(),
        lastSaved
      };
    }
    
    return { status: 'unknown', lastSaved: null };
  }

  async waitForAutoSave(timeout = 10000) {
    await this.page.waitForFunction(
      (selector) => {
        const indicator = document.querySelector(selector);
        return indicator && indicator.textContent.includes('Saved');
      },
      { timeout },
      this.selectors.autoSaveIndicator
    );
  }

  async toggleSidebarChat() {
    await this.page.click(this.selectors.chatToggle);
    await this.waitForElement(this.selectors.sidebarChat);
  }

  async startVideoCall() {
    await this.page.click(this.selectors.videoCallBtn);
    await this.waitForModal('Video Call');
  }

  async startScreenShare() {
    await this.page.click(this.selectors.screenShareBtn);
    // Note: Screen share requires user permission, so this might not work in automated tests
  }
}

module.exports = { CollaborationPage };