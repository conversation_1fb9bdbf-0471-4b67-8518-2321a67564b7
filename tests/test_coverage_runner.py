#!/usr/bin/env python3
"""
Comprehensive Test Coverage Runner for CLEAR Platform

This script runs all types of tests (unit, integration, E2E) and generates
a comprehensive coverage report including test attribute coverage.

Usage:
    python test_coverage_runner.py                    # Run all tests
    python test_coverage_runner.py --unit-only        # Run only unit tests
    python test_coverage_runner.py --e2e-only         # Run only E2E tests
    python test_coverage_runner.py --fast            # Skip slow tests
    python test_coverage_runner.py --coverage-html   # Generate HTML coverage
"""

import argparse
import json
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List


class TestCoverageRunner:
    """Comprehensive test runner with coverage analysis."""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.test_results = {}
        self.coverage_data = {}
        self.start_time = None
        self.end_time = None

    def run_python_tests(self, args: argparse.Namespace) -> Dict:
        """Run Python/Django tests with pytest."""
        print("🐍 Running Python/Django tests...")

        cmd = ["python", "-m", "pytest"]

        # Add coverage options
        if args.coverage_html:
            cmd.extend(
                ["--cov=apps", "--cov-report=html:reports/python-coverage", "--cov-report=json:reports/coverage.json"]
            )
        else:
            cmd.extend(["--cov=apps", "--cov-report=term-missing", "--cov-report=json:reports/coverage.json"])

        # Add test selection
        if args.unit_only:
            cmd.extend(["-m", "unit"])
        elif args.fast:
            cmd.extend(["-m", "not slow"])

        # Add verbosity and output options
        cmd.extend(
            [
                "-v",
                "--tb=short",
                "--junit-xml=reports/pytest-results.xml",
                "--html=reports/pytest-report.html",
                "--self-contained-html",
            ]
        )

        # Add test directories
        if not args.e2e_only:
            cmd.extend(["tests/", "apps/"])

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": time.time() - time.time(),  # Will be calculated properly
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "returncode": 124,
                "stdout": "",
                "stderr": "Tests timed out after 10 minutes",
                "duration": 600,
            }
        except Exception as e:
            return {"success": False, "returncode": 1, "stdout": "", "stderr": str(e), "duration": 0}

    def run_javascript_tests(self, args: argparse.Namespace) -> Dict:
        """Run JavaScript tests with Jest."""
        print("🟨 Running JavaScript tests...")

        if not (self.project_root / "package.json").exists():
            print("⚠️  No package.json found, skipping JavaScript tests")
            return {
                "success": True,
                "returncode": 0,
                "stdout": "No JavaScript tests to run",
                "stderr": "",
                "duration": 0,
                "skipped": True,
            }

        cmd = ["npm", "test"]

        # Add coverage options
        if args.coverage_html:
            cmd.append("--")
            cmd.extend(["--coverage", "--coverageDirectory=reports/jest-coverage"])

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300, cwd=self.project_root)

            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": time.time() - time.time(),  # Will be calculated properly
                "skipped": False,
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "returncode": 124,
                "stdout": "",
                "stderr": "JavaScript tests timed out after 5 minutes",
                "duration": 300,
                "skipped": False,
            }
        except Exception as e:
            return {"success": False, "returncode": 1, "stdout": "", "stderr": str(e), "duration": 0, "skipped": False}

    def run_e2e_tests(self, args: argparse.Namespace) -> Dict:
        """Run E2E tests with Playwright."""
        print("🎭 Running E2E tests...")

        if args.unit_only:
            return {
                "success": True,
                "returncode": 0,
                "stdout": "E2E tests skipped (unit-only mode)",
                "stderr": "",
                "duration": 0,
                "skipped": True,
            }

        # Check if Playwright is configured
        if not (self.project_root / "playwright.config.js").exists():
            print("⚠️  No playwright.config.js found, skipping E2E tests")
            return {
                "success": True,
                "returncode": 0,
                "stdout": "No E2E tests configured",
                "stderr": "",
                "duration": 0,
                "skipped": True,
            }

        cmd = ["npx", "playwright", "test"]

        # Add test selection
        if args.fast:
            cmd.extend(["--grep", "@fast"])

        # Add reporting options
        cmd.extend(["--reporter=html", "--output-dir=reports/playwright-results"])

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600, cwd=self.project_root)

            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": time.time() - time.time(),  # Will be calculated properly
                "skipped": False,
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "returncode": 124,
                "stdout": "",
                "stderr": "E2E tests timed out after 10 minutes",
                "duration": 600,
                "skipped": False,
            }
        except Exception as e:
            return {"success": False, "returncode": 1, "stdout": "", "stderr": str(e), "duration": 0, "skipped": False}

    def run_test_attribute_coverage(self) -> Dict:
        """Run test attribute coverage analysis."""
        print("🔍 Analyzing test attribute coverage...")

        coverage_script = self.project_root / "tests" / "test_attributes_coverage.py"
        if not coverage_script.exists():
            return {
                "success": False,
                "error": "Test attribute coverage script not found",
                "coverage_percentage": 0,
                "details": {},
            }

        try:
            cmd = [
                "python",
                str(coverage_script),
                "--report-format",
                "json",
                "--output-file",
                "reports/test-attributes-coverage.json",
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

            # Try to load the JSON report
            coverage_report_path = self.project_root / "reports" / "test-attributes-coverage.json"
            if coverage_report_path.exists():
                with open(coverage_report_path, "r") as f:
                    coverage_data = json.load(f)

                return {
                    "success": True,
                    "coverage_percentage": coverage_data.get("summary", {}).get("overall_coverage_percentage", 0),
                    "details": coverage_data,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                }
            else:
                return {
                    "success": False,
                    "error": "Coverage report not generated",
                    "coverage_percentage": 0,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                }

        except Exception as e:
            return {"success": False, "error": str(e), "coverage_percentage": 0, "details": {}}

    def setup_reports_directory(self):
        """Create reports directory if it doesn't exist."""
        reports_dir = self.project_root / "reports"
        reports_dir.mkdir(exist_ok=True)

        # Create subdirectories
        (reports_dir / "python-coverage").mkdir(exist_ok=True)
        (reports_dir / "jest-coverage").mkdir(exist_ok=True)
        (reports_dir / "playwright-results").mkdir(exist_ok=True)

    def generate_summary_report(self) -> Dict:
        """Generate a comprehensive summary report."""
        total_duration = (self.end_time - self.start_time) if self.start_time and self.end_time else 0

        # Calculate overall success
        overall_success = all(
            result.get("success", False) for result in self.test_results.values() if not result.get("skipped", False)
        )

        # Extract coverage data
        python_coverage = 0
        js_coverage = 0
        test_attr_coverage = 0

        # Try to read Python coverage
        try:
            coverage_json_path = self.project_root / "reports" / "coverage.json"
            if coverage_json_path.exists():
                with open(coverage_json_path, "r") as f:
                    coverage_data = json.load(f)
                    python_coverage = coverage_data.get("totals", {}).get("percent_covered", 0)
        except Exception:
            pass

        # Get test attribute coverage
        if "test_attributes" in self.test_results:
            test_attr_coverage = self.test_results["test_attributes"].get("coverage_percentage", 0)

        summary = {
            "timestamp": datetime.now().isoformat(),
            "overall_success": overall_success,
            "total_duration_seconds": total_duration,
            "test_results": self.test_results,
            "coverage": {
                "python_coverage_percentage": python_coverage,
                "javascript_coverage_percentage": js_coverage,
                "test_attributes_coverage_percentage": test_attr_coverage,
            },
            "recommendations": self.generate_recommendations(),
        }

        return summary

    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []

        # Check Python test results
        if "python" in self.test_results and not self.test_results["python"]["success"]:
            recommendations.append("🔴 Python tests failing - check test output for details")

        # Check JavaScript test results
        if "javascript" in self.test_results and not self.test_results["javascript"]["success"]:
            if not self.test_results["javascript"].get("skipped"):
                recommendations.append("🟨 JavaScript tests failing - check Jest output for details")

        # Check E2E test results
        if "e2e" in self.test_results and not self.test_results["e2e"]["success"]:
            if not self.test_results["e2e"].get("skipped"):
                recommendations.append("🎭 E2E tests failing - check Playwright output for details")

        # Check coverage levels
        if "test_attributes" in self.test_results:
            coverage = self.test_results["test_attributes"].get("coverage_percentage", 0)
            if coverage < 50:
                recommendations.append(
                    "🔍 Test attribute coverage is low - add data-testid to more interactive elements"
                )
            elif coverage < 80:
                recommendations.append(
                    "🔍 Test attribute coverage could be improved - consider adding more test identifiers"
                )

        # General recommendations
        if (
            len([r for r in self.test_results.values() if not r.get("success", False) and not r.get("skipped", False)])
            > 1
        ):
            recommendations.append("⚡ Multiple test suites failing - address high-priority issues first")

        if not recommendations:
            recommendations.append("✅ All tests passing - great job!")

        return recommendations

    def print_summary(self, summary: Dict):
        """Print a human-readable summary."""
        print("\n" + "=" * 80)
        print("🧪 COMPREHENSIVE TEST COVERAGE REPORT")
        print("=" * 80)

        print("\n📊 Overall Results:")
        print(f"  Success: {'✅ PASS' if summary['overall_success'] else '❌ FAIL'}")
        print(f"  Duration: {summary['total_duration_seconds']:.2f} seconds")
        print(f"  Timestamp: {summary['timestamp']}")

        print("\n🧪 Test Suite Results:")
        for test_type, result in summary["test_results"].items():
            if result.get("skipped"):
                status = "⏭️  SKIPPED"
            elif result.get("success"):
                status = "✅ PASS"
            else:
                status = "❌ FAIL"

            duration = result.get("duration", 0)
            print(f"  {test_type.capitalize():12} {status:12} ({duration:.2f}s)")

        print("\n📈 Coverage Summary:")
        coverage = summary["coverage"]
        print(f"  Python Code:     {coverage['python_coverage_percentage']:.1f}%")
        print(f"  JavaScript:      {coverage['javascript_coverage_percentage']:.1f}%")
        print(f"  Test Attributes: {coverage['test_attributes_coverage_percentage']:.1f}%")

        print("\n💡 Recommendations:")
        for rec in summary["recommendations"]:
            print(f"  • {rec}")

        print("\n📁 Report Files:")
        reports_dir = self.project_root / "reports"
        if reports_dir.exists():
            for report_file in reports_dir.glob("*.html"):
                print(f"  • {report_file.name}")
            for report_file in reports_dir.glob("*.xml"):
                print(f"  • {report_file.name}")
            for report_file in reports_dir.glob("*.json"):
                print(f"  • {report_file.name}")

    def save_summary_report(self, summary: Dict):
        """Save the summary report to a JSON file."""
        reports_dir = self.project_root / "reports"
        summary_file = reports_dir / "test-coverage-summary.json"

        with open(summary_file, "w") as f:
            json.dump(summary, f, indent=2)

        print(f"\n📄 Summary report saved: {summary_file}")

    def run_all_tests(self, args: argparse.Namespace):
        """Run all test suites and generate comprehensive report."""
        self.start_time = time.time()
        self.setup_reports_directory()

        print("🚀 Starting comprehensive test coverage analysis...")
        print(f"Project root: {self.project_root}")

        # Run test suites
        if not args.e2e_only:
            self.test_results["python"] = self.run_python_tests(args)
            self.test_results["javascript"] = self.run_javascript_tests(args)

        if not args.unit_only:
            self.test_results["e2e"] = self.run_e2e_tests(args)

        # Run test attribute coverage analysis
        self.test_results["test_attributes"] = self.run_test_attribute_coverage()

        self.end_time = time.time()

        # Generate and display summary
        summary = self.generate_summary_report()
        self.print_summary(summary)
        self.save_summary_report(summary)

        # Exit with appropriate code
        exit_code = 0 if summary["overall_success"] else 1
        print(f"\n🏁 Test coverage analysis complete (exit code: {exit_code})")

        return exit_code


def main():
    parser = argparse.ArgumentParser(description="Run comprehensive test coverage analysis")
    parser.add_argument("--unit-only", action="store_true", help="Run only unit tests")
    parser.add_argument("--e2e-only", action="store_true", help="Run only E2E tests")
    parser.add_argument("--fast", action="store_true", help="Skip slow tests")
    parser.add_argument("--coverage-html", action="store_true", help="Generate HTML coverage reports")
    parser.add_argument("--project-root", default=".", help="Project root directory")

    args = parser.parse_args()

    # Validate arguments
    if args.unit_only and args.e2e_only:
        print("❌ Cannot specify both --unit-only and --e2e-only")
        sys.exit(1)

    # Run tests
    runner = TestCoverageRunner(args.project_root)
    exit_code = runner.run_all_tests(args)

    sys.exit(exit_code)


if __name__ == "__main__":
    main()
