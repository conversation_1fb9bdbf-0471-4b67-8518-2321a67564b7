"""
from ..models import Document
from ..models import Message
from ..models import Project
from ..models import Task
from ..models import UserProfile
from typing import ClassVar
Test Fixtures for HTMX Integration Tests

Provides comprehensive test data and mock objects for testing all HTMX implementations.
Includes factories for creating test data, mock WebSocket connections, and test files.
"""

from datetime import timedel<PERSON>

from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone

from apps.core.models import (
    ChatMessage,
    Conversation,
    Document,
    Organization,
    Project,
    Task,
    UserProfile,
)

User = get_user_model()


class HTMXTestFixtures:
    """
    Centralized fixture creation for HTMX integration tests.
    """

    @staticmethod
    def create_test_organization(**kwargs):
        """Create a test organization."""
        defaults = {
            "name": "Test Organization",
            "description": "A test organization for HTMX tests",
            "is_active": True,
        }
        defaults.update(kwargs)
        return Organization.objects.create(**defaults)

    @staticmethod
    def create_test_user(organization=None, **kwargs):
        """Create a test user with optional organization."""
        if organization is None:
            organization = HTMXTestFixtures.create_test_organization()

        defaults = {
            "username": f"testuser_{timezone.now().microsecond}",
            "email": f"test_{timezone.now().microsecond}@example.com",
            "first_name": "Test",
            "last_name": "User",
            "is_active": True,
            "organization": organization,
        }
        defaults.update(kwargs)

        password = defaults.pop("password", "testpass123")
        user = User.objects.create_user(password=password, **defaults)

        # Create user profile if it doesn't exist
        if not hasattr(user, "profile"):
            UserProfile.objects.create(
                user=user,
                phone_number="555-0123",
                department="Engineering",
                role="Developer",
            )

        return user

    @staticmethod
    def create_test_project(organization=None, created_by=None, **kwargs):
        """Create a test project."""
        if organization is None:
            organization = HTMXTestFixtures.create_test_organization()

        if created_by is None:
            created_by = HTMXTestFixtures.create_test_user(organization=organization)

        defaults = {
            "name": f"Test Project {timezone.now().microsecond}",
            "description": "A test project for HTMX integration tests",
            "organization": organization,
            "created_by": created_by,
            "status": "active",
            "priority": "medium",
            "project_type": "infrastructure",
            "budget": 100000,
            "timeline_months": 6,
            "location": "Test Location",
        }
        defaults.update(kwargs)

        return Project.objects.create(**defaults)

    @staticmethod
    def create_test_document(project=None, uploaded_by=None, **kwargs):
        """Create a test document."""
        if project is None:
            project = HTMXTestFixtures.create_test_project()

        if uploaded_by is None:
            uploaded_by = project.created_by

        # Create test file content
        test_content = b"This is test document content for HTMX integration tests."
        test_file = SimpleUploadedFile("test_document.pdf", test_content, content_type="application/pdf")

        defaults = {
            "title": f"Test Document {timezone.now().microsecond}",
            "description": "A test document for HTMX tests",
            "project": project,
            "uploaded_by": uploaded_by,
            "file": test_file,
            "file_type": "pdf",
            "file_size": len(test_content),
            "is_public": False,
        }
        defaults.update(kwargs)

        return Document.objects.create(**defaults)

    @staticmethod
    def create_test_conversation(created_by=None, participants=None, **kwargs):
        """Create a test conversation."""
        if created_by is None:
            created_by = HTMXTestFixtures.create_test_user()

        defaults = {
            "name": f"Test Conversation {timezone.now().microsecond}",
            "description": "A test conversation for HTMX tests",
            "created_by": created_by,
            "conversation_type": "group",
            "is_active": True,
        }
        defaults.update(kwargs)

        conversation = Conversation.objects.create(**defaults)

        # Add participants
        if participants is None:
            participants = [created_by]

        for participant in participants:
            conversation.participants.add(participant)

        return conversation

    @staticmethod
    def create_test_message(conversation=None, user=None, **kwargs):
        """Create a test chat message."""
        if conversation is None:
            conversation = HTMXTestFixtures.create_test_conversation()

        if user is None:
            user = conversation.created_by

        defaults = {
            "conversation": conversation,
            "user": user,
            "content": f"Test message content {timezone.now().microsecond}",
            "message_type": "text",
            "timestamp": timezone.now(),
        }
        defaults.update(kwargs)

        return ChatMessage.objects.create(**defaults)

    @staticmethod
    def create_test_task(project=None, assigned_to=None, created_by=None, **kwargs):
        """Create a test task."""
        if project is None:
            project = HTMXTestFixtures.create_test_project()

        if created_by is None:
            created_by = project.created_by

        if assigned_to is None:
            assigned_to = created_by

        defaults = {
            "title": f"Test Task {timezone.now().microsecond}",
            "description": "A test task for HTMX integration tests",
            "project": project,
            "assigned_to": assigned_to,
            "created_by": created_by,
            "status": "pending",
            "priority": "medium",
            "due_date": timezone.now() + timedelta(days=7),
            "estimated_hours": 8,
        }
        defaults.update(kwargs)

        return Task.objects.create(**defaults)


class MockWebSocketConsumer:
    """
    Mock WebSocket consumer for testing real-time features.
    """

    def __init__(self, user=None):
        self.user = user or HTMXTestFixtures.create_test_user()
        self.connected = False
        self.messages_sent = []
        self.groups = []

    async def connect(self):
        """Simulate WebSocket connection."""
        self.connected = True
        return True

    async def disconnect(self, close_code):
        """Simulate WebSocket disconnection."""
        self.connected = False
        self.groups.clear()

    async def send(self, text_data=None, bytes_data=None):
        """Simulate sending message to WebSocket."""
        if text_data:
            self.messages_sent.append(
                {
                    "type": "text",
                    "data": text_data,
                    "timestamp": timezone.now().isoformat(),
                }
            )
        elif bytes_data:
            self.messages_sent.append(
                {
                    "type": "bytes",
                    "data": bytes_data,
                    "timestamp": timezone.now().isoformat(),
                }
            )

    async def group_add(self, group_name):
        """Simulate adding to group."""
        if group_name not in self.groups:
            self.groups.append(group_name)

    async def group_discard(self, group_name):
        """Simulate removing from group."""
        if group_name in self.groups:
            self.groups.remove(group_name)

    def get_sent_messages(self):
        """Get all messages sent to this consumer."""
        return self.messages_sent.copy()

    def clear_messages(self):
        """Clear sent messages history."""
        self.messages_sent.clear()


class MockFileUpload:
    """
    Mock file upload for testing drag-drop functionality.
    """

    def __init__(self, filename, content, content_type, size=None):
        self.filename = filename
        self.content = content if isinstance(content, bytes) else content.encode("utf-8")
        self.content_type = content_type
        self.size = size or len(self.content)
        self.chunks_uploaded = 0
        self.total_chunks = 1
        self.upload_progress = 0

    def to_uploaded_file(self):
        """Convert to Django UploadedFile."""
        return SimpleUploadedFile(self.filename, self.content, content_type=self.content_type)

    def simulate_chunked_upload(self, chunk_size=1024):
        """Simulate chunked file upload."""
        chunks = []
        for i in range(0, len(self.content), chunk_size):
            chunk = self.content[i : i + chunk_size]
            chunks.append(chunk)

        self.total_chunks = len(chunks)
        return chunks

    def update_progress(self, uploaded_bytes):
        """Update upload progress."""
        self.upload_progress = min(100, (uploaded_bytes / self.size) * 100)
        return self.upload_progress


class AutosaveTestData:
    """
    Test data generator for autosave functionality.
    """

    @staticmethod
    def generate_form_data(size="medium"):
        """Generate test form data of various sizes."""
        data_sets = {
            "small": {"name": "Test Project", "description": "A simple test project"},
            "medium": {
                "name": "Medium Test Project",
                "description": "A medium-sized test project with more details",
                "project_type": "infrastructure",
                "priority": "high",
                "location": "Test Location",
                "budget": "100000",
                "timeline_months": "6",
            },
            "large": {
                "name": "Large Test Project",
                "description": "A large test project with comprehensive details and extensive information",
                "project_type": "infrastructure",
                "priority": "high",
                "location": "Test Location with detailed address information",
                "budget": "500000",
                "timeline_months": "12",
                "objectives": "Detailed project objectives and goals",
                "stakeholders": "List of project stakeholders",
                "risks": "Identified project risks and mitigation strategies",
                "resources": "Required project resources and equipment",
                "milestones": "Project milestones and deliverables",
            },
        }

        return data_sets.get(size, data_sets["medium"])

    @staticmethod
    def generate_autosave_session_data():
        """Generate autosave session data."""
        return {
            "form_data": AutosaveTestData.generate_form_data("medium"),
            "metadata": {
                "timestamp": timezone.now().isoformat(),
                "user_id": 1,
                "session_key": "test_session_key",
                "field_count": 7,
                "version": 1,
                "user_agent": "Test User Agent",
                "ip_address": "127.0.0.1",
            },
            "expires_at": (timezone.now() + timedelta(hours=1)).isoformat(),
        }


class WizardTestData:
    """
    Test data generator for wizard functionality.
    """

    @staticmethod
    def get_wizard_steps_config():
        """Get test wizard steps configuration."""
        return [
            {
                "id": "basic_info",
                "title": "Basic Information",
                "template": "wizards/basic_info.html",
                "validation_required": True,
                "fields": ["name", "description"],
            },
            {
                "id": "project_details",
                "title": "Project Details",
                "template": "wizards/project_details.html",
                "validation_required": True,
                "fields": ["project_type", "priority", "location"],
            },
            {
                "id": "budget_timeline",
                "title": "Budget & Timeline",
                "template": "wizards/budget_timeline.html",
                "validation_required": True,
                "fields": ["budget", "timeline_months"],
            },
        ]

    @staticmethod
    def get_step_data(step_index):
        """Get test data for a specific wizard step."""
        step_data = [
            {"name": "Test Project", "description": "A comprehensive test project"},
            {
                "project_type": "infrastructure",
                "priority": "high",
                "location": "Test Location",
            },
            {"budget": "100000", "timeline_months": "6"},
        ]

        return step_data[step_index] if step_index < len(step_data) else {}


class InfiniteScrollTestData:
    """
    Test data generator for infinite scroll functionality.
    """

    @staticmethod
    def create_test_projects(count=50, organization=None, created_by=None):
        """Create multiple test projects for pagination testing."""
        if organization is None:
            organization = HTMXTestFixtures.create_test_organization()

        if created_by is None:
            created_by = HTMXTestFixtures.create_test_user(organization=organization)

        projects = []
        for i in range(count):
            project = HTMXTestFixtures.create_test_project(
                organization=organization,
                created_by=created_by,
                name=f"Test Project {i + 1}",
                description=f"Test project number {i + 1} for infinite scroll testing",
            )
            projects.append(project)

        return projects

    @staticmethod
    def generate_pagination_response(page, per_page=20, total_items=100):
        """Generate mock pagination response data."""
        start_index = (page - 1) * per_page
        end_index = min(start_index + per_page, total_items)

        items = []
        for i in range(start_index, end_index):
            items.append(
                {
                    "id": i + 1,
                    "name": f"Item {i + 1}",
                    "description": f"Description for item {i + 1}",
                }
            )

        has_next = end_index < total_items
        next_page = page + 1 if has_next else None

        return {
            "items": items,
            "page": page,
            "per_page": per_page,
            "total_items": total_items,
            "has_next": has_next,
            "next_page": next_page,
            "total_pages": (total_items + per_page - 1) // per_page,
        }


class DragDropTestFiles:
    """
    Test file generator for drag-drop upload functionality.
    """

    @staticmethod
    def create_test_files(count=3):
        """Create various test files for upload testing."""
        files = []

        file_configs = [
            {
                "name": "test_document.pdf",
                "content": b"%PDF-1.4\nTest PDF content",
                "content_type": "application/pdf",
            },
            {
                "name": "test_image.jpg",
                "content": b"\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01Test JPEG",
                "content_type": "image/jpeg",
            },
            {
                "name": "test_document.docx",
                "content": b"PK\x03\x04Test DOCX content",
                "content_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            },
            {
                "name": "test_spreadsheet.xlsx",
                "content": b"PK\x03\x04Test XLSX content",
                "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            },
            {
                "name": "test_text.txt",
                "content": b"This is a test text file for upload testing.",
                "content_type": "text/plain",
            },
        ]

        for i in range(min(count, len(file_configs))):
            config = file_configs[i]
            files.append(
                MockFileUpload(
                    filename=config["name"],
                    content=config["content"],
                    content_type=config["content_type"],
                )
            )

        return files

    @staticmethod
    def create_large_file(size_mb=50):
        """Create a large test file for upload testing."""
        content = b"X" * (size_mb * 1024 * 1024)  # Create file of specified size
        return MockFileUpload(
            filename=f"large_file_{size_mb}mb.bin",
            content=content,
            content_type="application/octet-stream",
        )

    @staticmethod
    def create_invalid_file():
        """Create an invalid file for error testing."""
        # Create a file with an invalid extension
        return MockFileUpload(
            filename="malicious_file.exe",
            content=b"This is a potentially malicious file",
            content_type="application/x-executable",
        )


class NavigationTestData:
    """
    Test data for navigation state testing.
    """

    @staticmethod
    def get_initial_nav_state():
        """Get initial navigation state."""
        return {
            "dropdowns": {},
            "sidebar": {"collapsed": False, "activeSection": None, "pinnedItems": []},
            "breadcrumbs": [],
            "activeMenu": {"primary": None, "secondary": None, "tertiary": None},
            "tabs": {},
            "accordions": {},
            "preferences": {
                "sidebarAutoCollapse": False,
                "dropdownHoverDelay": 300,
                "breadcrumbStyle": "full",
            },
        }

    @staticmethod
    def get_sample_breadcrumbs():
        """Get sample breadcrumb data."""
        return [
            {"title": "Dashboard", "url": "/dashboard/", "active": False},
            {"title": "Projects", "url": "/projects/", "active": False},
            {"title": "Test Project", "url": "/projects/1/", "active": True},
        ]


# Utility functions for test fixtures


def setup_test_data():
    """Set up comprehensive test data for all HTMX tests."""
    # Create organization and users
    org = HTMXTestFixtures.create_test_organization()
    admin_user = HTMXTestFixtures.create_test_user(
        organization=org,
        username="admin",
        email="<EMAIL>",
        is_staff=True,
        is_superuser=True,
    )
    regular_user = HTMXTestFixtures.create_test_user(organization=org, username="user", email="<EMAIL>")

    # Create projects
    projects = []
    for i in range(10):
        project = HTMXTestFixtures.create_test_project(
            organization=org, created_by=admin_user, name=f"Test Project {i + 1}"
        )
        projects.append(project)

    # Create documents
    for project in projects[:3]:  # Add documents to first 3 projects
        HTMXTestFixtures.create_test_document(project=project, uploaded_by=admin_user)

    # Create conversation and messages
    conversation = HTMXTestFixtures.create_test_conversation(
        created_by=admin_user, participants=[admin_user, regular_user]
    )

    for i in range(5):
        HTMXTestFixtures.create_test_message(
            conversation=conversation,
            user=admin_user if i % 2 == 0 else regular_user,
            content=f"Test message {i + 1}",
        )

    # Create tasks
    for project in projects[:2]:  # Add tasks to first 2 projects
        for i in range(3):
            HTMXTestFixtures.create_test_task(
                project=project,
                assigned_to=regular_user,
                created_by=admin_user,
                title=f"Task {i + 1} for {project.name}",
            )

    return {
        "organization": org,
        "admin_user": admin_user,
        "regular_user": regular_user,
        "projects": projects,
        "conversation": conversation,
    }


def cleanup_test_data():
    """Clean up all test data."""
    # Clear all test data
    models_to_clear = [
        ChatMessage,
        Conversation,
        Document,
        Task,
        Project,
        UserProfile,
        User,
        Organization,
    ]

    for model in models_to_clear:
        try:
            model.objects.all().delete()
        except BaseException:
            pass  # Ignore errors during cleanup
